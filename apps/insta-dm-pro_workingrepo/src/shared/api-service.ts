/**
 * API Service for Chrome Extension
 * Handles communication with the AISetter backend API
 */

import { environment } from './environment';

export interface ApiKeyVerificationResult {
  success: boolean;
  data?: {
    organizationId: string;
    organizationName: string;
    organizationSlug: string;
  };
  message?: string;
}

export interface PendingFollowUp {
  id: string;
  contactId: string;
  recipientId: string;
  username: string;
  message: string;
  scheduledTime: string;
  followUpNumber: number;
}

export interface PendingFollowUpsResult {
  success: boolean;
  data?: PendingFollowUp[];
  message?: string;
}

export interface MarkFollowUpResult {
  success: boolean;
  message?: string;
}

export interface AttackListItem {
  id: string;
  instagramId: string | null;
  username: string;
  avatar: string | null;
  priority: number;
  status: string;
  attackListStatus: string | null;
  nextMessageAt: Date | null;
  lastInteractionAt: Date | null;
  stage: string;
  conversationSource: string;
  createdAt: Date;
  updatedAt: Date;
  batchMessageStatus: string | null;
  currentMessageSequence: number | null;
  lastMessageSentAt: Date | null;
  batchId: string | null;
  messageStatus?: {
    sent: number;
    total: number;
    currentSequence: number;
  };
  suggestedMessage?: {
    batchId: string;
    batchName: string;
    text: string;
  };
  plannedMessages?: {
    type: 'ai_followups' | 'batch_messages';
    messages: Array<{
      id?: string;
      sequenceNumber: number;
      message: string;
      scheduledTime?: Date;
      status?: string;
      delayMinutes?: number;
    }>;
  };
}

export interface AttackListResult {
  success: boolean;
  data?: AttackListItem[];
  total?: number;
  message?: string;
}

export interface ChromeExtensionSettings {
  timeBetweenDMsMin: number;
  timeBetweenDMsMax: number;
  messagesBeforeBreakMin: number;
  messagesBeforeBreakMax: number;
  breakDurationMin: number;
  breakDurationMax: number;
  pauseStart: string;
  pauseStop: string;
  smartFocus: boolean;
  isConnected: boolean;
  lastConnectionAt?: Date;
  extensionStatus: string;
  // Scraping timing settings
  scrapingIntervalMin: number; // Minutes between scraping sessions
  scrapingIntervalMax: number; // Max minutes between scraping sessions
  nextScrapingAllowedAt?: string; // ISO timestamp when next scraping is allowed
  scrapingEnabled: boolean; // Whether automatic scraping is enabled
  currentActivity?: string;
  lastActivityAt?: Date;
}

export interface ChromeExtensionSettingsResult {
  success: boolean;
  data?: ChromeExtensionSettings;
  message?: string;
}

// Enhanced status types - supports dynamic milestone statuses
export type ExtensionStatusType =
  | 'FRESH_START'
  | 'ACTIVE'
  | 'IDLE'
  | 'STOPPED'
  | 'ALL_SCRAPED'
  | `SCRAPED_${number}` // Dynamic milestone statuses (SCRAPED_250, SCRAPED_500, etc.)
  | 'SCRAPED_250_FOLLOWERS' // Legacy status for backward compatibility
  | 'CONVERSATIONS_GATHERING' // Backend is processing Instagram conversations
  | 'CONVERSATIONS_GATHERED_READY'; // All conversations processed, ready for attack list creation

export interface ExtensionStatus {
  extensionStatus: ExtensionStatusType;
  currentActivity?: string;
  lastActivityAt?: Date;
  isConnected: boolean;
  lastConnectionAt?: Date;
  // Enhanced tracking fields
  totalFollowersScraped?: number;
  lastScrapedPosition?: number;
  lastScrapedUsernames?: string[];
  scrapingTargetReached?: boolean;
  allFollowersScraped?: boolean;
  lastScrapingSession?: Date;
}

export interface ExtensionStatusResult {
  success: boolean;
  data?: ExtensionStatus;
  message?: string;
}

export interface ScrapedFollower {
  instagramNickname: string;
  instagramId?: string;
  avatar?: string;
  followerCount?: number;
  isVerified: boolean;
}

export interface SendFollowersResult {
  success: boolean;
  data?: {
    processed: number;
    skipped: number;
    total: number;
  };
  message?: string;
}

export interface LastScrapedFollowersResult {
  success: boolean;
  data?: ScrapedFollower[];
  message?: string;
}

export interface MessageToSend {
  id: string;
  username: string;
  message: string;
  type: 'batch' | 'followup';
  followUpId?: string;
  sequenceNumber?: number;
}

export interface MessagesToSendResult {
  success: boolean;
  data?: MessageToSend[];
  count?: number;
  metadata?: {
    totalReadyContacts: number;
    smartFocusEnabled: boolean;
    timestamp: string;
  };
  message?: string;
}

export interface MarkSentRequest {
  contactId: string;
  messageType: 'batch' | 'batch_sequence' | 'followup';
  followUpId?: string;
  sequenceNumber?: number;
  batchCompleted?: boolean;
}

export interface MarkSentResult {
  success: boolean;
  data?: {
    contactId: string;
    attackListStatus: string;
    nextMessageAt: Date | null;
    priority: number;
    lastInteractionAt: Date;
  };
  message?: string;
}

export interface ScrapingEligibilityResult {
  success: boolean;
  data?: {
    isEligible: boolean;
    nextAllowedAt: Date | null;
    waitTimeMs: number;
    waitTimeHuman: string;
    intervalDays: number;
    lastScrapingSession: Date | null;
    totalFollowersScraped: number;
    extensionStatus: string;
    allFollowersScraped: boolean;
  };
  message?: string;
}

export interface ConversationGatheringStatus {
  isGathering: boolean;
  isComplete: boolean;
  progress?: {
    totalContacts: number;
    processedContacts: number;
    percentComplete: number;
  };
  estimatedTimeRemaining?: string;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}

export interface ConversationGatheringResult {
  success: boolean;
  data?: ConversationGatheringStatus;
  message?: string;
}

export interface ContactStatus {
  id: string;
  instagramNickname: string;
  stage: string;
  attackListStatus: string | null;
  priority: number;
  nextMessageAt: Date | null;
}

export interface ContactStatusResult {
  success: boolean;
  data?: ContactStatus;
  message?: string;
}

export class ApiService {
  private static readonly BASE_URL = environment.apiBaseUrl;

  /**
   * Get default headers for API requests
   */
  private static getHeaders(apiKey?: string) {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true',
    };
    
    if (apiKey) {
      headers['X-API-Key'] = apiKey;
    }
    
    return headers;
  }

  /**
   * Verify API key with the backend
   */
  static async verifyApiKey(apiKey: string): Promise<ApiKeyVerificationResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/verify-api-key`, {
        method: 'POST',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error verifying API key:', error);
      return {
        success: false,
        message: 'Failed to connect to API server'
      };
    }
  }

  /**
   * Fetch pending follow-ups from the backend
   */
  static async fetchPendingFollowUps(apiKey: string): Promise<PendingFollowUpsResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/pending-follow-ups`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching pending follow-ups:', error);
      return {
        success: false,
        message: 'Failed to fetch pending follow-ups'
      };
    }
  }

  /**
   * Mark a follow-up as sent
   */
  static async markFollowUpSent(apiKey: string, followUpId: string): Promise<MarkFollowUpResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/follow-ups/${followUpId}/mark-sent`, {
        method: 'POST',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error marking follow-up as sent:', error);
      return {
        success: false,
        message: 'Failed to mark follow-up as sent'
      };
    }
  }

  /**
   * Mark a follow-up as failed
   */
  static async markFollowUpFailed(apiKey: string, followUpId: string, errorMessage?: string): Promise<MarkFollowUpResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/follow-ups/${followUpId}/mark-failed`, {
        method: 'POST',
        headers: this.getHeaders(apiKey),
        body: JSON.stringify({
          error: errorMessage
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error marking follow-up as failed:', error);
      return {
        success: false,
        message: 'Failed to mark follow-up as failed'
      };
    }
  }

  /**
   * Get attack list for Chrome extension
   */
  static async getAttackList(apiKey: string, limit?: number): Promise<AttackListResult> {
    try {
      console.log('🎯 API: Fetching attack list...');
      
      // First verify the API key to get the organization ID
      const verificationResult = await this.verifyApiKey(apiKey);
      if (!verificationResult.success || !verificationResult.data?.organizationId) {
        console.error('🎯 API: Failed to verify API key or get organization ID');
        return {
          success: false,
          message: 'Failed to verify API key or get organization ID'
        };
      }

      const organizationId = verificationResult.data.organizationId;
      console.log('🎯 API: Organization ID:', organizationId);
      
      const url = new URL(`${this.BASE_URL}/api/chrome-extension/attack-list`);
      url.searchParams.set('organizationId', organizationId);
      if (limit) {
        url.searchParams.set('limit', limit.toString());
      }
      
      console.log('🎯 API: Requesting URL:', url.toString());
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      console.log('🎯 API: Response status:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('🎯 API: Error response:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🎯 API: Attack list result:', result);
      return result;
    } catch (error) {
      console.error('🎯 API: Exception fetching attack list:', error);
      return {
        success: false,
        message: 'Failed to fetch attack list'
      };
    }
  }

  /**
   * Get Chrome extension settings
   */
  static async getChromeExtensionSettings(apiKey: string): Promise<ChromeExtensionSettingsResult> {
    try {
      console.log('🔧 API: Fetching Chrome extension settings...');
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/settings`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      console.log('🔧 API: Settings response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔧 API: Settings error response:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🔧 API: Settings result:', result);
      return result;
    } catch (error) {
      console.error('🔧 API: Exception fetching settings:', error);
      return {
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Update message status for Chrome extension
   */
  static async updateMessageStatus(apiKey: string, contactId: string, status: string, sequence?: number): Promise<MarkFollowUpResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/message-status`, {
        method: 'POST',
        headers: this.getHeaders(apiKey),
        body: JSON.stringify({
          contactId,
          status,
          sequence
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating message status:', error);
      return {
        success: false,
        message: 'Failed to update message status'
      };
    }
  }


  /**
   * Get extension status
   */
  static async getExtensionStatus(apiKey: string): Promise<ExtensionStatusResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/status`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching extension status:', error);
      return {
        success: false,
        message: 'Failed to fetch extension status'
      };
    }
  }

  /**
   * Send scraped followers to the app with enhanced tracking
   */
  static async sendScrapedFollowers(
    apiKey: string,
    followers: ScrapedFollower[],
    startPosition: number,
    totalFollowers?: number,
    isComplete?: boolean,
    lastScrapedUsernames?: string[],
    isInitialScraping?: boolean
  ): Promise<SendFollowersResult> {
    try {
      console.log('🚨 DEBUG: Sending followers to API', {
        endpoint: `${this.BASE_URL}/api/chrome-extension/process-followers`,
        followersCount: followers.length,
        startPosition,
        totalFollowers,
        isComplete,
        apiKeyPresent: !!apiKey
      });

      const requestBody = {
        followers,
        startPosition,
        totalFollowers,
        isComplete: isComplete || false,
        lastScrapedUsernames: lastScrapedUsernames || [],
        isInitialScraping: isInitialScraping || false
      };

      console.log('🚨 DEBUG: Request body sample:', {
        followersCount: followers.length,
        firstFollower: followers[0],
        startPosition,
        totalFollowers,
        isComplete,
        lastScrapedUsernamesCount: lastScrapedUsernames?.length || 0
      });

      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/process-followers`, {
        method: 'POST',
        headers: this.getHeaders(apiKey),
        body: JSON.stringify(requestBody)
      });

      console.log('🚨 DEBUG: API Response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🚨 DEBUG: API Error response:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🚨 DEBUG: API Success response:', result);
      return result;
    } catch (error) {
      console.error('🚨 ERROR: Exception sending scraped followers:', error);
      return {
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get last scraped followers for resume functionality
   */
  static async getLastScrapedFollowers(apiKey: string, limit: number = 10): Promise<LastScrapedFollowersResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/last-scraped-followers?limit=${limit}`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching last scraped followers:', error);
      return {
        success: false,
        message: 'Failed to fetch last scraped followers'
      };
    }
  }

  /**
   * Update extension status with enhanced tracking
   */
  static async updateExtensionStatus(
    apiKey: string,
    extensionStatus: string,
    currentActivity?: string,
    isConnected?: boolean,
    enhancedTracking?: {
      totalFollowersScraped?: number;
      lastScrapedPosition?: number;
      lastScrapedUsernames?: string[];
      scrapingTargetReached?: boolean;
      allFollowersScraped?: boolean;
    }
  ): Promise<MarkFollowUpResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/status`, {
        method: 'PUT',
        headers: this.getHeaders(apiKey),
        body: JSON.stringify({
          extensionStatus,
          currentActivity,
          isConnected,
          ...enhancedTracking
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating extension status:', error);
      return {
        success: false,
        message: 'Failed to update extension status'
      };
    }
  }

  /**
   * Get messages ready to send from the backend
   */
  static async getMessagesToSend(apiKey: string): Promise<MessagesToSendResult> {
    try {
      console.log('🔍 API: Fetching messages to send...');
      console.log('🔍 API: API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'undefined');
      
      // First verify the API key to get the organization ID
      const verificationResult = await this.verifyApiKey(apiKey);
      console.log('🔍 API: Verification result:', verificationResult);
      
      if (!verificationResult.success || !verificationResult.data?.organizationId) {
        console.error('🔍 API: Failed to verify API key or get organization ID');
        return {
          success: false,
          message: 'Failed to verify API key or get organization ID'
        };
      }

      const organizationId = verificationResult.data.organizationId;
      console.log('🔍 API: Organization ID:', organizationId);
      
      const url = new URL(`${this.BASE_URL}/api/chrome-extension/messages-to-send`);
      url.searchParams.set('organizationId', organizationId);
      console.log('🔍 API: Requesting URL:', url.toString());
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      console.log('🔍 API: Response status:', response.status, response.statusText);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔍 API: Error response:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🔍 API: Messages result:', result);
      return result;
    } catch (error) {
      console.error('🔍 API: Exception fetching messages:', error);
      return {
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Mark message as sent and schedule follow-ups
   */
  static async markMessageSent(apiKey: string, data: MarkSentRequest): Promise<MarkSentResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/mark-sent`, {
        method: 'POST',
        headers: this.getHeaders(apiKey),
        body: JSON.stringify(data)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error marking message as sent:', error);
      return {
        success: false,
        message: 'Failed to mark message as sent'
      };
    }
  }

  /**
   * Check if scraping is allowed based on interval rules
   */
  static async checkScrapingEligibility(apiKey: string): Promise<ScrapingEligibilityResult> {
    try {
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/scraping-eligibility`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error checking scraping eligibility:', error);
      return {
        success: false,
        message: 'Failed to check scraping eligibility'
      };
    }
  }

  /**
   * Check conversation gathering status
   */
  static async checkConversationGatheringStatus(apiKey: string): Promise<ConversationGatheringResult> {
    try {
      console.log('🔄 API: Checking conversation gathering status...');
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/conversation-gathering-status`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔄 API: Conversation gathering status error:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🔄 API: Conversation gathering status result:', result);
      return result;
    } catch (error) {
      console.error('🔄 API: Exception checking conversation gathering status:', error);
      return {
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get contact status to check if contact is disqualified before sending messages
   */
  static async getContactStatus(apiKey: string, contactId: string): Promise<ContactStatusResult> {
    try {
      console.log('🔍 API: Checking contact status for:', contactId);
      const response = await fetch(`${this.BASE_URL}/api/chrome-extension/contact-status/${contactId}`, {
        method: 'GET',
        headers: this.getHeaders(apiKey)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔍 API: Contact status error:', errorText);
        return {
          success: false,
          message: `API Error ${response.status}: ${errorText}`
        };
      }

      const result = await response.json();
      console.log('🔍 API: Contact status result:', result);
      return result;
    } catch (error) {
      console.error('🔍 API: Exception checking contact status:', error);
      return {
        success: false,
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
