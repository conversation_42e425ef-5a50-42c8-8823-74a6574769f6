import { APP_NAME } from '@workspace/common/app';

export function createTitle(title: string, addSuffix: boolean = true): string {
  if (!addSuffix) {
    return title;
  }
  if (!title) {
    return APP_NAME;
  }

  return `${title} | ${APP_NAME}`;
}

export function capitalize(str: string): string {
  if (!str) {
    return str;
  }

  if (str.length === 1) {
    return str.charAt(0).toUpperCase();
  }

  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function getInitials(name: string): string {
  if (!name) {
    return '';
  }
  return name
    .replace(/\s+/, ' ')
    .split(' ')
    .slice(0, 2)
    .map((v) => v && v[0].toUpperCase())
    .join('');
}



/**
 * Format Instagram timestamp for display
 * Handles ISO 8601 strings, Unix timestamps (seconds), and JavaScript timestamps (milliseconds)
 */
export function formatInstagramTimestamp(timestamp: string | number | Date): Date {
  if (timestamp instanceof Date) {
    return timestamp;
  }

  if (typeof timestamp === 'string') {
    // First try to parse as ISO 8601 date string (Instagram API format: 2025-05-25T15:16:04+0000)
    if (timestamp.includes('T') || timestamp.includes('-')) {
      const isoDate = new Date(timestamp);
      if (!isNaN(isoDate.getTime())) {
        return isoDate;
      }
    }

    // Fallback: try to parse as Unix timestamp string
    const parsed = parseInt(timestamp);
    if (!isNaN(parsed)) {
      // Instagram API sometimes returns Unix timestamp in seconds
      return new Date(parsed * 1000);
    }

    // If all parsing fails, return current time
    return new Date();
  }

  if (typeof timestamp === 'number') {
    // If the number is less than a reasonable threshold, assume it's in seconds
    // Otherwise, assume it's in milliseconds
    if (timestamp < 1e10) {
      // Unix timestamp in seconds (before year 2286)
      return new Date(timestamp * 1000);
    } else {
      // JavaScript timestamp in milliseconds
      return new Date(timestamp);
    }
  }

  // Fallback to current time
  return new Date();
}

/**
 * Validate and normalize timestamp for database storage
 */
export function normalizeTimestamp(timestamp: string | number | Date | null | undefined): Date {
  if (!timestamp) {
    return new Date();
  }

  return formatInstagramTimestamp(timestamp);
}
