import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag } from 'next/cache';
import { Caching, OrganizationCacheKey } from '~/data/caching';

/**
 * Internal API endpoint to invalidate cache tags
 * This is used by the Instagram bot package to invalidate cache when contacts are updated
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    const body = await req.json();
    const { organizationId, cacheKeys, secret } = body;

    // Verify the secret to prevent unauthorized cache invalidation
    const expectedSecret = process.env.CACHE_INVALIDATION_SECRET || 'cache-secret-123';
    if (secret !== expectedSecret) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!organizationId) {
      return NextResponse.json({ error: 'Missing organizationId' }, { status: 400 });
    }

    const invalidatedTags: string[] = [];

    // If specific cache keys are provided, invalidate those
    if (cacheKeys && Array.isArray(cacheKeys)) {
      for (const cacheKey of cacheKeys) {
        // Check if the cache key exists in the enum
        if (cacheKey in OrganizationCacheKey) {
          const enumValue = OrganizationCacheKey[cacheKey as keyof typeof OrganizationCacheKey];
          const tag = Caching.createOrganizationTag(enumValue, organizationId);
          revalidateTag(tag);
          invalidatedTags.push(tag);
        } else {
          console.warn(`Unknown cache key: ${cacheKey} - cache key has been removed or doesn't exist`);
        }
      }
    } else {
      // Default behavior: no cache invalidation (Instagram contacts cache removed)
      console.log(`No cache keys specified and default Instagram contacts cache has been removed for organization: ${organizationId}`);
    }

    console.log(`Cache invalidated for organization ${organizationId}:`, invalidatedTags);

    return NextResponse.json({
      success: true,
      invalidatedTags,
      organizationId
    });
  } catch (error) {
    console.error('Error invalidating cache:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
