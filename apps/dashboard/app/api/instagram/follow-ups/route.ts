import { NextRequest, NextResponse } from 'next/server';

import { auth } from '~/auth';
import { prisma } from '@workspace/database/client';
import { isWithin24HourWindow, getLatest24HourTime } from '@workspace/instagram-bot';

/**
 * Helper function to get organization from referer
 */
async function getOrganizationFromRequest(req: NextRequest) {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  // Get the organization slug from the referer URL
  const referer = req.headers.get('referer') || '';
  const match = referer.match(/\/organizations\/([^/]+)\//i);
  const orgSlug = match ? match[1] : null;

  if (!orgSlug) {
    throw new Error('Organization not found');
  }

  // Get the organization ID from the slug
  const organization = await prisma.organization.findFirst({
    where: { slug: orgSlug }
  });

  if (!organization) {
    throw new Error('Organization not found');
  }

  // Check if the user has access to the organization
  const userMembership = await prisma.membership.findFirst({
    where: {
      organizationId: organization.id,
      userId: session.user.id,
    },
  });

  if (!userMembership) {
    throw new Error('Unauthorized');
  }

  return { organization, session };
}

/**
 * Get all follow-ups for the organization
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const { organization } = await getOrganizationFromRequest(req);

    // Get new follow-ups from InstagramFollowUp table (exclude external/conversation gathering follow-ups)
    const newFollowUps = await prisma.instagramFollowUp.findMany({
      where: {
        status: { in: ['pending', 'sent', 'failed'] }, // Exclude external status (conversation gathering follow-ups)
        InstagramContact: {
          organizationId: organization.id
        }
      },
      include: {
        InstagramContact: {
          select: {
            instagramNickname: true,
            avatar: true,
            lastInteractionAt: true
          }
        }
      },
      orderBy: [
        { scheduledTime: 'asc' },
        { sequenceNumber: 'asc' }
      ]
    });

    // Get legacy follow-ups from InstagramContact table
    const legacyContacts = await prisma.instagramContact.findMany({
      where: {
        organizationId: organization.id,
        OR: [
          { followUpMessage1: { not: null } },
          { followUpMessage2: { not: null } },
          { followUpMessage3: { not: null } },
          { followUpMessage4: { not: null } }
        ]
      },
      select: {
        id: true,
        instagramNickname: true,
        avatar: true,
        followUpMessage1: true,
        followUpTime1: true,
        followUpStatus1: true,
        followUpMessage2: true,
        followUpTime2: true,
        followUpStatus2: true,
        followUpMessage3: true,
        followUpTime3: true,
        followUpStatus3: true,
        followUpMessage4: true,
        followUpTime4: true,
        followUpStatus4: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    // Transform new follow-ups with 24-hour window information
    const transformedNewFollowUps = newFollowUps.map(followUp => {
      const contact = followUp.InstagramContact;
      const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, followUp.scheduledTime);
      const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);

      return {
        id: followUp.id,
        contactId: followUp.contactId,
        contactUsername: contact.instagramNickname,
        contactAvatar: contact.avatar,
        sequenceNumber: followUp.sequenceNumber,
        message: followUp.message,
        scheduledTime: followUp.scheduledTime,
        status: followUp.status,
        sentAt: followUp.sentAt,
        createdAt: followUp.createdAt,
        updatedAt: followUp.updatedAt,
        type: 'new',
        isWithin24HourWindow: isWithinWindow,
        latest24HourTime: latest24HourTime?.toISOString() || null
      };
    });

    // Transform legacy follow-ups
    const transformedLegacyFollowUps = [];

    for (const contact of legacyContacts) {
      // Follow-up 1
      if (contact.followUpMessage1) {
        transformedLegacyFollowUps.push({
          id: `${contact.id}_fu1`,
          contactId: contact.id,
          contactUsername: contact.instagramNickname,
          contactAvatar: contact.avatar,
          followUpNumber: 1,
          message: contact.followUpMessage1,
          scheduledTime: contact.followUpTime1,
          status: contact.followUpStatus1 || 'pending',
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt,
          type: 'legacy'
        });
      }

      // Follow-up 2
      if (contact.followUpMessage2) {
        transformedLegacyFollowUps.push({
          id: `${contact.id}_fu2`,
          contactId: contact.id,
          contactUsername: contact.instagramNickname,
          contactAvatar: contact.avatar,
          followUpNumber: 2,
          message: contact.followUpMessage2,
          scheduledTime: contact.followUpTime2,
          status: contact.followUpStatus2 || 'pending',
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt,
          type: 'legacy'
        });
      }

      // Follow-up 3
      if (contact.followUpMessage3) {
        transformedLegacyFollowUps.push({
          id: `${contact.id}_fu3`,
          contactId: contact.id,
          contactUsername: contact.instagramNickname,
          contactAvatar: contact.avatar,
          followUpNumber: 3,
          message: contact.followUpMessage3,
          scheduledTime: contact.followUpTime3,
          status: contact.followUpStatus3 || 'pending',
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt,
          type: 'legacy'
        });
      }

      // Follow-up 4
      if (contact.followUpMessage4) {
        transformedLegacyFollowUps.push({
          id: `${contact.id}_fu4`,
          contactId: contact.id,
          contactUsername: contact.instagramNickname,
          contactAvatar: contact.avatar,
          followUpNumber: 4,
          message: contact.followUpMessage4,
          scheduledTime: contact.followUpTime4,
          status: contact.followUpStatus4 || 'pending',
          createdAt: contact.createdAt,
          updatedAt: contact.updatedAt,
          type: 'legacy'
        });
      }
    }

    // Sort legacy follow-ups by scheduled time
    transformedLegacyFollowUps.sort((a, b) => {
      if (!a.scheduledTime && !b.scheduledTime) return 0;
      if (!a.scheduledTime) return 1;
      if (!b.scheduledTime) return -1;
      return new Date(a.scheduledTime).getTime() - new Date(b.scheduledTime).getTime();
    });

    const response = NextResponse.json({
      success: true,
      data: {
        newFollowUps: transformedNewFollowUps,
        legacyFollowUps: transformedLegacyFollowUps
      }
    });

    // Add cache headers to prevent browser caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error('Error fetching follow-ups:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Update a follow-up
 */
export async function PATCH(req: NextRequest): Promise<Response> {
  try {
    const { followUpId, message, scheduledTime, delayHours } = await req.json();

    if (!followUpId || !message || (!scheduledTime && !delayHours)) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const { organization } = await getOrganizationFromRequest(req);

    // Check if it's a new follow-up (UUID) or legacy follow-up (contactId_fuN)
    if (followUpId.includes('_fu')) {
      // Legacy follow-up
      const [contactId, followUpType] = followUpId.split('_');
      const followUpNumber = parseInt(followUpType.replace('fu', ''));

      if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
        return NextResponse.json(
          { success: false, error: 'Invalid follow-up ID' },
          { status: 400 }
        );
      }

      // Check if contact exists and belongs to the organization
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          User: {
            memberships: {
              some: {
                organizationId: organization.id
              }
            }
          }
        },
        select: {
          id: true,
          lastInteractionAt: true,
          followUpStatus1: true,
          followUpStatus2: true,
          followUpStatus3: true,
          followUpStatus4: true,
          followUpStatus5: true,
          followUpStatus6: true
        }
      });

      if (!contact) {
        return NextResponse.json(
          { success: false, error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Calculate the new time based on scheduledTime or delayHours
      let newTime: Date;
      if (scheduledTime) {
        newTime = new Date(scheduledTime);
      } else {
        newTime = new Date();
        newTime.setHours(newTime.getHours() + delayHours);
      }

      // Check if the follow-up is within the 24-hour window
      const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, newTime);
      const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);

      if (!isWithinWindow && latest24HourTime) {
        return NextResponse.json({
          success: false,
          error: `Follow-up scheduled outside 24-hour window. Latest allowed time: ${latest24HourTime.toISOString()}`,
          latest24HourTime: latest24HourTime.toISOString()
        }, { status: 400 });
      }

      // Update the follow-up
      const updateData: Record<string, unknown> = {};
      updateData[`followUpMessage${followUpNumber}`] = message;
      updateData[`followUpTime${followUpNumber}`] = newTime;

      // Only update status if it's not already sent
      const statusField = `followUpStatus${followUpNumber}`;
      if (contact[statusField as keyof typeof contact] !== 'sent') {
        updateData[statusField] = 'pending';
      }

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: updateData
      });
    } else {
      // New follow-up
      const followUp = await prisma.instagramFollowUp.findFirst({
        where: {
          id: followUpId,
          InstagramContact: {
            User: {
              memberships: {
                some: {
                  organizationId: organization.id
                }
              }
            }
          }
        },
        include: {
          InstagramContact: {
            select: {
              id: true,
              lastInteractionAt: true
            }
          }
        }
      });

      if (!followUp) {
        return NextResponse.json(
          { success: false, error: 'Follow-up not found' },
          { status: 404 }
        );
      }

      // Calculate the new time based on scheduledTime or delayHours
      let newTime: Date;
      if (scheduledTime) {
        newTime = new Date(scheduledTime);
      } else {
        newTime = new Date();
        newTime.setHours(newTime.getHours() + delayHours);
      }

      // Check if the follow-up is within the 24-hour window
      const isWithinWindow = isWithin24HourWindow(followUp.InstagramContact.lastInteractionAt, newTime);
      const latest24HourTime = getLatest24HourTime(followUp.InstagramContact.lastInteractionAt);

      if (!isWithinWindow && latest24HourTime) {
        return NextResponse.json({
          success: false,
          error: `Follow-up scheduled outside 24-hour window. Latest allowed time: ${latest24HourTime.toISOString()}`,
          latest24HourTime: latest24HourTime.toISOString()
        }, { status: 400 });
      }

      await prisma.instagramFollowUp.update({
        where: { id: followUpId },
        data: {
          message,
          scheduledTime: newTime,
          status: followUp.status === 'sent' ? 'sent' : 'pending'
        }
      });
    }

    // Cache invalidation removed - Instagram contacts no longer cached

    return NextResponse.json({
      success: true,
      message: 'Follow-up updated successfully'
    });
  } catch (error) {
    console.error('Error updating follow-up:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Delete a follow-up
 */
export async function DELETE(req: NextRequest): Promise<Response> {
  try {
    const { followUpId } = await req.json();

    if (!followUpId) {
      return NextResponse.json(
        { success: false, error: 'Follow-up ID is required' },
        { status: 400 }
      );
    }

    const { organization } = await getOrganizationFromRequest(req);

    // Check if it's a new follow-up (UUID) or legacy follow-up (contactId_fuN)
    if (followUpId.includes('_fu')) {
      // Legacy follow-up
      const [contactId, followUpType] = followUpId.split('_');
      const followUpNumber = parseInt(followUpType.replace('fu', ''));

      if (isNaN(followUpNumber) || followUpNumber < 1 || followUpNumber > 4) {
        return NextResponse.json(
          { success: false, error: 'Invalid follow-up ID' },
          { status: 400 }
        );
      }

      // Check if contact exists and belongs to the organization
      const contact = await prisma.instagramContact.findFirst({
        where: {
          id: contactId,
          User: {
            memberships: {
              some: {
                organizationId: organization.id
              }
            }
          }
        }
      });

      if (!contact) {
        return NextResponse.json(
          { success: false, error: 'Contact not found' },
          { status: 404 }
        );
      }

      // Clear the follow-up fields
      const updateData: Record<string, unknown> = {};
      updateData[`followUpMessage${followUpNumber}`] = null;
      updateData[`followUpTime${followUpNumber}`] = null;
      updateData[`followUpStatus${followUpNumber}`] = null;

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: updateData
      });
    } else {
      // New follow-up
      const followUp = await prisma.instagramFollowUp.findFirst({
        where: {
          id: followUpId,
          InstagramContact: {
            User: {
              memberships: {
                some: {
                  organizationId: organization.id
                }
              }
            }
          }
        }
      });

      if (!followUp) {
        return NextResponse.json(
          { success: false, error: 'Follow-up not found' },
          { status: 404 }
        );
      }

      await prisma.instagramFollowUp.delete({
        where: { id: followUpId }
      });
    }

    // Cache invalidation removed - Instagram contacts no longer cached

    return NextResponse.json({
      success: true,
      message: 'Follow-up deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting follow-up:', error);
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }
      if (error.message === 'Organization not found') {
        return NextResponse.json(
          { success: false, error: 'Organization not found' },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
