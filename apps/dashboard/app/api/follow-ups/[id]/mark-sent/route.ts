import { NextRequest, NextResponse } from 'next/server';
import { verifyApi<PERSON>ey } from '@workspace/api-keys';
import { prisma } from '@workspace/database/client';

/**
 * Mark a follow-up as sent
 * This endpoint is used by the Chrome extension to mark follow-up messages as sent
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
): Promise<Response> {
  try {
    console.log('🚨 DEBUG: Mark-sent API called with ID:', params.id);
    console.log('🚨 DEBUG: Request headers:', Object.fromEntries(req.headers.entries()));
    console.log('🚨 DEBUG: Request method:', req.method);
    console.log('🚨 DEBUG: Request URL:', req.url);

    const { id } = params;

    // Test database connection first
    console.log('🚨 DEBUG: Testing database connection...');
    try {
      await prisma.$queryRaw`SELECT 1 as test`;
      console.log('🚨 DEBUG: Database connection successful');
    } catch (dbError) {
      console.error('🚨 ERROR: Database connection failed:', dbError);
      return NextResponse.json(
        { success: false, message: 'Database connection error', error: String(dbError) },
        { status: 500 }
      );
    }

    // Get API key from header
    const apiKey = req.headers.get('X-API-Key');
    console.log('🚨 DEBUG: API key present:', !!apiKey);
    console.log('🚨 DEBUG: API key length:', apiKey?.length);
    console.log('🚨 DEBUG: API key prefix:', apiKey?.substring(0, 4));

    if (!apiKey) {
      console.log('🚨 DEBUG: No API key provided');
      return NextResponse.json(
        { success: false, message: 'API key is missing' },
        { status: 401 }
      );
    }

    // Verify the API key
    console.log('🚨 DEBUG: Verifying API key...');
    let result;
    try {
      result = await verifyApiKey(apiKey);
      console.log('🚨 DEBUG: API key verification result:', result);
    } catch (verifyError) {
      console.error('🚨 ERROR: API key verification threw exception:', verifyError);
      return NextResponse.json(
        { success: false, message: 'API key verification error', error: String(verifyError) },
        { status: 500 }
      );
    }

    if (!result.success) {
      console.log('🚨 DEBUG: API key verification failed:', result.errorMessage);
      return NextResponse.json(
        { success: false, message: result.errorMessage },
        { status: 401 }
      );
    }

    const organizationId = result.organizationId;
    console.log('🚨 DEBUG: Organization ID:', organizationId);

    // Process follow-up (UUID format only)
    console.log('🚨 DEBUG: Processing follow-up with UUID format');
    console.log('🚨 DEBUG: Searching for follow-up with ID:', id);
    console.log('🚨 DEBUG: Organization ID:', organizationId);

    let followUp;
    try {
      console.log('🚨 DEBUG: Executing Prisma query for InstagramFollowUp...');
      followUp = await prisma.instagramFollowUp.findFirst({
        where: {
          id,
          InstagramContact: {
            organizationId
          }
        },
        include: {
          InstagramContact: {
            select: {
              id: true,
              instagramNickname: true,
              organizationId: true
            }
          }
        }
      });
      console.log('🚨 DEBUG: Prisma query completed successfully');
    } catch (queryError) {
      console.error('🚨 ERROR: Prisma query failed:', queryError);
      console.error('🚨 ERROR: Query error details:', {
        message: queryError instanceof Error ? queryError.message : String(queryError),
        stack: queryError instanceof Error ? queryError.stack : 'No stack trace'
      });
      return NextResponse.json(
        { success: false, message: 'Database query error', error: String(queryError) },
        { status: 500 }
      );
    }

    console.log('🚨 DEBUG: Follow-up found:', !!followUp);
    if (followUp) {
      console.log('🚨 DEBUG: Follow-up details:', {
        id: followUp.id,
        contactId: followUp.contactId,
        status: followUp.status,
        message: followUp.message.substring(0, 50) + '...'
      });
    }

    if (!followUp) {
      console.log('🚨 DEBUG: Follow-up not found in database');
      return NextResponse.json(
        { success: false, message: 'Follow-up not found' },
        { status: 404 }
      );
    }

    // Update the follow-up status to sent
    console.log('🚨 DEBUG: Updating follow-up status to sent...');
    try {
      await prisma.instagramFollowUp.update({
        where: { id },
        data: {
          status: 'sent',
          sentAt: new Date()
        }
      });
      console.log('🚨 DEBUG: Follow-up status updated successfully');
    } catch (updateError) {
      console.error('🚨 ERROR: Failed to update follow-up status:', updateError);
      return NextResponse.json(
        { success: false, message: 'Failed to update follow-up status', error: String(updateError) },
        { status: 500 }
      );
    }

    // Save the message to the database
    console.log('🚨 DEBUG: Creating Instagram message record...');
    try {
      await prisma.instagramMessage.create({
        data: {
          contactId: followUp.contactId,
          messageId: `follow-up-${id}-${Date.now()}`,
          content: followUp.message,
          isFromUser: false,
          timestamp: new Date()
        }
      });
      console.log('🚨 DEBUG: Instagram message record created successfully');
    } catch (messageError) {
      console.error('🚨 ERROR: Failed to create Instagram message:', messageError);
      return NextResponse.json(
        { success: false, message: 'Failed to create message record', error: String(messageError) },
        { status: 500 }
      );
    }

    // Cache invalidation removed - Instagram contacts no longer cached
    console.log('🚨 DEBUG: Cache invalidated for organization:', followUp.InstagramContact.organizationId);

    console.log('🚨 DEBUG: Returning success response');
    return NextResponse.json({
      success: true,
      message: `Follow-up marked as sent`
    });
  } catch (error) {
    console.error('🚨 ERROR: Error marking follow-up as sent:', error);
    console.error('🚨 ERROR: Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('🚨 ERROR: Follow-up ID:', params.id);
    console.error('🚨 ERROR: API Key provided:', !!req.headers.get('X-API-Key'));

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        debug: process.env.NODE_ENV === 'development' ? {
          error: error instanceof Error ? error.message : String(error),
          followUpId: params.id
        } : undefined
      },
      { status: 500 }
    );
  }
}
