import { NextResponse, type NextRequest } from 'next/server';
import { createSearchParamsCache, parseAsString } from 'nuqs/server';
import { validate as uuidValidate } from 'uuid';

import { Prisma } from '@workspace/database';
import { prisma } from '@workspace/database/client';

const paramsCache = createSearchParamsCache({
  contactId: parseAsString.withDefault('')
});

export async function GET(
  req: NextRequest,
  props: { params: Promise<NextParams> }
): Promise<Response> {
  const { contactId } = await paramsCache.parse(props.params);
  if (!contactId || !uuidValidate(contactId)) {
    return new NextResponse(undefined, {
      status: 400,
      headers: {
        'Cache-Control': 'no-store'
      }
    });
  }

  // Check if this is an Instagram contact with an avatar URL
  const instagramContact = await prisma.instagramContact.findFirst({
    where: { id: contactId },
    select: {
      avatar: true
    }
  });

  // If we have an Instagram contact with an avatar URL, redirect to it
  if (instagramContact?.avatar) {
    return NextResponse.redirect(instagramContact.avatar, 302);
  }

  // No contact image found
  return new NextResponse(undefined, {
    status: 404,
    headers: {
      'Cache-Control': 'no-store'
    }
  });
}
