import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { PromptConfigDto } from '~/types/dtos/prompt-config-dto';

export async function getPromptConfig(): Promise<PromptConfigDto | null> {
  const ctx = await getAuthOrganizationContext();

  const promptConfig = await prisma.promptConfig.findFirst({
        where: { organizationId: ctx.organization.id },
        select: {
          id: true,
          aboutUs: true,
          qualificationQuestions: true,
          additionalInfo: true,
          botStyleId: true,
          youtubeLink: true,
          websiteLink: true,
          leadMagnetLink: true,
          conversionLink: true,
          botStyle: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!promptConfig) {
        return null;
      }

      return {
        id: promptConfig.id,
        aboutUs: promptConfig.aboutUs || undefined,
        qualificationQuestions: promptConfig.qualificationQuestions || undefined,
        additionalInfo: promptConfig.additionalInfo || undefined,
        botStyleId: promptConfig.botStyleId || undefined,
        botStyleName: promptConfig.botStyle?.name,
        youtubeLink: promptConfig.youtubeLink || undefined,
        websiteLink: promptConfig.websiteLink || undefined,
        leadMagnetLink: promptConfig.leadMagnetLink || undefined,
        conversionLink: promptConfig.conversionLink
      };

}
