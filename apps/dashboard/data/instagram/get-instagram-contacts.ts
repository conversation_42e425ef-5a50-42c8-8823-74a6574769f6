import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { InstagramContactDto } from '~/types/dtos/instagram-contact-dto';
import { SortDirection } from '~/types/sort-direction';

export async function getInstagramContacts(): Promise<InstagramContactDto[]> {
  const ctx = await getAuthOrganizationContext();

  const contacts = await prisma.instagramContact.findMany({
        where: {
          organizationId: ctx.organization.id
        },
        select: {
          id: true,
          instagramId: true,
          instagramNickname: true,
          avatar: true,
          messageCount: true,
          stage: true,
          isIgnored: true,
          isConversionLinkSent: true,
          isTakeControl: true,
          followUpMessage1: true,
          followUpTime1: true,
          followUpStatus1: true,
          followUpMessage2: true,
          followUpTime2: true,
          followUpStatus2: true,
          followUpMessage3: true,
          followUpTime3: true,
          followUpStatus3: true,
          followUpMessage4: true,
          followUpTime4: true,
          followUpStatus4: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: {
          updatedAt: SortDirection.Desc
        }
      });

      return contacts.map(contact => ({
        id: contact.id,
        instagramId: contact.instagramId,
        instagramUsername: contact.instagramNickname || 'Unknown User',
        avatar: contact.avatar,
        messageCount: contact.messageCount,
        stage: contact.stage,
        isIgnored: contact.isIgnored,
        isLinkSent: contact.isConversionLinkSent,
        isTakeControl: contact.isTakeControl,
        followUps: [
          ...(contact.followUpMessage1 ? [{
            message: contact.followUpMessage1,
            time: contact.followUpTime1,
            status: contact.followUpStatus1 || '',
            type: 'legacy' as const,
            id: `${contact.id}_fu1`,
            sequenceNumber: 1
          }] : []),
          ...(contact.followUpMessage2 ? [{
            message: contact.followUpMessage2,
            time: contact.followUpTime2,
            status: contact.followUpStatus2 || '',
            type: 'legacy' as const,
            id: `${contact.id}_fu2`,
            sequenceNumber: 2
          }] : []),
          ...(contact.followUpMessage3 ? [{
            message: contact.followUpMessage3,
            time: contact.followUpTime3,
            status: contact.followUpStatus3 || '',
            type: 'legacy' as const,
            id: `${contact.id}_fu3`,
            sequenceNumber: 3
          }] : []),
          ...(contact.followUpMessage4 ? [{
            message: contact.followUpMessage4,
            time: contact.followUpTime4,
            status: contact.followUpStatus4 || '',
            type: 'legacy' as const,
            id: `${contact.id}_fu4`,
            sequenceNumber: 4
          }] : [])
        ],
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt
      }));

}
