import 'server-only';

import { prisma } from '@workspace/database/client';
import { BotStyleDto } from '~/types/dtos/instagram/bot-style-dto';

export async function getBotStyles(organizationId?: string): Promise<BotStyleDto[]> {
  const botStyles = await prisma.botStyle.findMany({
        select: {
          id: true,
          name: true,
          description: true,
          promptText: true,
          isDefault: true
        },
        orderBy: [
          { isDefault: 'desc' },
          { name: 'asc' }
        ]
      });

      return botStyles.map(style => ({
        id: style.id,
        name: style.name,
        description: style.description || undefined,
        promptText: style.promptText,
        isDefault: style.isDefault
      }));

  return botStyles.map(style => ({
    id: style.id,
    name: style.name,
    description: style.description || undefined,
    promptText: style.promptText,
    isDefault: style.isDefault
  }));
}
