import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { getBusinessAccountInfo } from '~/lib/instagram-client';

const defaultRevalidateTimeInSeconds = 3600; // 1 hour

export interface InstagramBusinessInfoDto {
  id: string;
  username: string;
  name: string;
  profilePictureUrl?: string;
  followersCount: number;
  mediaCount: number;
  accountType: string;
  biography?: string;
  lastUpdated: string;
}

export async function getInstagramBusinessInfo(): Promise<InstagramBusinessInfoDto | null> {
  const ctx = await getAuthOrganizationContext();

  return getInstagramBusinessInfoForOrganization(ctx.organization.id);
}

export async function getInstagramBusinessInfoForOrganization(organizationId: string): Promise<InstagramBusinessInfoDto | null> {
      try {
        // Get Instagram settings to get the access token
        const instagramSettings = await prisma.instagramSettings.findFirst({
          where: {
            organizationId: organizationId,
            instagramToken: { not: null }
          },
          select: {
            instagramToken: true
          }
        });

        if (!instagramSettings?.instagramToken) {
          console.log('No Instagram token found for organization:', organizationId);
          return null;
        }

        // Get business account info from Instagram API
        const businessInfo = await getBusinessAccountInfo(instagramSettings.instagramToken);

        return {
          id: businessInfo.id,
          username: businessInfo.username || '',
          name: businessInfo.name || '',
          profilePictureUrl: businessInfo.profile_picture_url,
          followersCount: businessInfo.followers_count || 0,
          mediaCount: businessInfo.media_count || 0,
          accountType: businessInfo.account_type || 'BUSINESS',
          biography: businessInfo.biography,
          lastUpdated: new Date().toISOString()
        };
      } catch (error) {
        console.error('Error fetching Instagram business info:', error);

        // Return null instead of throwing to allow graceful degradation
        return null;
      }
}

export async function getInstagramFollowerCount(): Promise<number> {
  try {
    const ctx = await getAuthOrganizationContext();
    return getInstagramFollowerCountForOrganization(ctx.organization.id);
  } catch (error) {
    console.error('Error getting Instagram follower count:', error);
    return 0;
  }
}

export async function getInstagramFollowerCountForOrganization(organizationId: string): Promise<number> {
  try {
    const businessInfo = await getInstagramBusinessInfoForOrganization(organizationId);
    console.log('getInstagramFollowerCountForOrganization - businessInfo:', businessInfo);
    const followerCount = businessInfo?.followersCount || 0;
    console.log('getInstagramFollowerCountForOrganization - returning:', followerCount);
    return followerCount;
  } catch (error) {
    console.error('Error getting Instagram follower count for organization:', error);
    return 0;
  }
}
