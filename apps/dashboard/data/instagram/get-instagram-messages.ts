import 'server-only';

import { unstable_noStore as noStore } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';

import { InstagramMessageDto } from '~/types/dtos/instagram-message-dto';
import { debugStore } from '@workspace/instagram-bot/debug-store';

export async function getInstagramMessages(contactId: string): Promise<InstagramMessageDto[]> {
  // Disable caching to always get fresh data
  noStore();

  const ctx = await getAuthOrganizationContext();

  // First verify that the contact belongs to the organization
  const contact = await prisma.instagramContact.findFirst({
    where: {
      id: contactId,
      organizationId: ctx.organization.id
    },
    select: { id: true }
  });

  if (!contact) {
    return [];
  }

  const messages = await prisma.instagramMessage.findMany({
    where: { contactId },
    select: {
      id: true,
      messageId: true,
      content: true,
      isFromUser: true,
      isFromExtension: true,
      messageType: true,
      isEcho: true,
      mediaType: true,
      mediaUrl: true,
      mediaDescription: true,
      timestamp: true
    },
    orderBy: {
      timestamp: 'asc'
    }
  });

  return messages.map(message => {
    // Get debug information for bot messages
    const debugInfo = !message.isFromUser ? debugStore.get(message.messageId) : undefined;

    return {
      id: message.id,
      messageId: message.messageId,
      content: message.content,
      isFromUser: message.isFromUser,
      isFromExtension: message.isFromExtension,
      messageType: message.messageType,
      isEcho: message.isEcho,
      mediaType: message.mediaType || undefined,
      mediaUrl: message.mediaUrl || undefined,
      mediaDescription: message.mediaDescription || undefined,
      timestamp: message.timestamp,
      debugInfo: debugInfo || undefined
    };
  });
}
