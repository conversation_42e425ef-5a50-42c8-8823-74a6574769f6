export interface InstagramMessageDto {
  id: string;
  messageId: string;
  content: string;
  isFromUser: boolean;
  isFromExtension?: boolean;
  messageType: 'RECEIVED' | 'AI_SENT' | 'MANUAL_SENT';
  isEcho?: boolean;
  mediaType?: string;
  mediaUrl?: string;
  mediaDescription?: string;
  timestamp: Date;
  debugInfo?: {
    thinking?: string | null;
    fullPrompt?: string;
    conversationHistory?: string;
    rawResponse?: unknown;
  };
}
