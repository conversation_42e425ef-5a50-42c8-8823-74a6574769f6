'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const toggleIgnoreContactSchema = z.object({
  contactId: z.string().uuid()
});

export const toggleIgnoreContact = authOrganizationActionClient
  .metadata({ actionName: 'toggleIgnoreContact' })
  .schema(toggleIgnoreContactSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      },
      select: {
        id: true,
        isIgnored: true
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    const newIgnoredState = !contact.isIgnored;

    // Prepare update data
    const updateData: any = { isIgnored: newIgnoredState };

    // If we're ignoring the contact, clear all follow-ups
    if (newIgnoredState) {
      updateData.fu1_message = null;
      updateData.fu1_time = null;
      updateData.fu1_status = null;
      updateData.fu2_message = null;
      updateData.fu2_time = null;
      updateData.fu2_status = null;
      updateData.fu3_message = null;
      updateData.fu3_time = null;
      updateData.fu3_status = null;
      updateData.fu4_message = null;
      updateData.fu4_time = null;
      updateData.fu4_status = null;
    }

    // Update the contact
    await prisma.instagramContact.update({
      where: { id: contact.id },
      data: updateData
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id,
        contact.id
      )
    );

    // Cache invalidation removed - Instagram contacts no longer cached

    return { success: true };
  });
