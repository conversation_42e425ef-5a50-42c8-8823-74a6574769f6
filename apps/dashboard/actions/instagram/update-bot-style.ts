'use server';

import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';

const schema = z.object({
  id: z.string(),
  name: z.string().min(1),
  description: z.string().min(1),
  promptText: z.string().min(1),
  isDefault: z.boolean().default(false)
});

export const updateBotStyle = authOrganizationActionClient
  .metadata({ actionName: 'updateBotStyle' })
  .schema(schema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to update bot styles
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Only SaaS admin can update bot styles');
    }

    // Check if bot style exists
    const existingStyle = await prisma.botStyle.findUnique({
      where: {
        id: parsedInput.id
      }
    });

    if (!existingStyle) {
      throw new NotFoundError('Bot style not found');
    }

    // If this is set as default, unset any existing default
    if (parsedInput.isDefault && !existingStyle.isDefault) {
      await prisma.botStyle.updateMany({
        where: {
          isDefault: true
        },
        data: {
          isDefault: false
        }
      });
    }

    // Update the bot style
    const botStyle = await prisma.botStyle.update({
      where: {
        id: parsedInput.id
      },
      data: {
        name: parsedInput.name,
        description: parsedInput.description,
        promptText: parsedInput.promptText,
        isDefault: parsedInput.isDefault
      }
    });

    // Cache invalidation removed - Bot styles no longer cached

    return botStyle;
  });
