'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';
import { sendMessage } from '~/lib/instagram-client';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const sendManualMessageSchema = z.object({
  contactId: z.string().uuid(),
  message: z.string().min(1)
});

export const sendManualMessage = authOrganizationActionClient
  .metadata({ actionName: 'sendManualMessage' })
  .schema(sendManualMessageSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact and make sure it belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      },
      select: {
        id: true,
        instagramId: true,
        isTakeControl: true,
        Organization: {
          select: {
            InstagramSettings: {
              select: {
                instagramToken: true
              }
            }
          }
        }
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    if (!contact.isTakeControl) {
      throw new Error('You must take control of the conversation before sending manual messages');
    }

    const instagramToken = contact.Organization.InstagramSettings?.instagramToken;
    if (!instagramToken) {
      throw new Error('Instagram token not configured');
    }

    if (!contact.instagramId) {
      throw new Error('Contact Instagram ID not available');
    }

    // Generate a unique message ID
    const messageId = `manual-${uuidv4()}`;

    // Save the message to the database
    await prisma.instagramMessage.create({
      data: {
        contactId: contact.id,
        messageId,
        content: parsedInput.message,
        isFromUser: false
      }
    });

    // Update message count (do not update lastInteractionAt for manual bot messages)
    await prisma.instagramContact.update({
      where: { id: contact.id },
      data: {
        messageCount: { increment: 1 },
        updatedAt: new Date()
      }
    });

    // Send the message to Instagram
    await sendMessage({
      recipientId: contact.instagramId,
      message: parsedInput.message,
      accessToken: instagramToken
    });

    // Cache invalidation removed - Instagram caches no longer used
    // Only revalidate InstagramContact cache which still exists
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id,
        contact.id
      )
    );

    return { success: true };
  });
