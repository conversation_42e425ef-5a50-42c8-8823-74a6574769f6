'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const toggleTakeControlSchema = z.object({
  contactId: z.string().uuid()
});

export const toggleTakeControl = authOrganizationActionClient
  .metadata({ actionName: 'toggleTakeControl' })
  .schema(toggleTakeControlSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      },
      select: {
        id: true,
        isTakeControl: true
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    // Toggle the isTakeControl flag
    await prisma.instagramContact.update({
      where: { id: contact.id },
      data: { isTakeControl: !contact.isTakeControl }
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id,
        contact.id
      )
    );
    
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id
      )
    );

    return { success: true };
  });
