'use server';

import { z } from 'zod';
import { revalidateTag } from 'next/cache';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';
import {
  getInstagramStatistics,
  getInstagramStatisticsSummary
} from '~/data/instagram/get-instagram-statistics';
import { getInstagramBusinessInfo } from '~/data/instagram/get-instagram-business-info';

const getStatisticsSchema = z.object({
  dateRange: z.enum(['last_day', 'last_7_days', 'last_30_days', 'custom']).default('last_30_days'),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  includeIgnored: z.boolean().default(false),
  includeTakeControl: z.boolean().default(false)
});

const refreshStatisticsSchema = z.object({
  organizationId: z.string().optional()
});

/**
 * Get Instagram statistics with filters
 */
export const getInstagramStatisticsAction = authOrganizationActionClient
  .metadata({ actionName: 'getInstagramStatistics' })
  .schema(getStatisticsSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const statistics = await getInstagramStatistics(parsedInput);
      return { success: true, data: statistics };
    } catch (error) {
      console.error('Error fetching Instagram statistics:', error);
      return {
        success: false,
        error: 'Failed to fetch statistics. Please try again.'
      };
    }
  });

/**
 * Get Instagram statistics summary for home dashboard
 */
export const getInstagramStatisticsSummaryAction = authOrganizationActionClient
  .metadata({ actionName: 'getInstagramStatisticsSummary' })
  .schema(z.object({}))
  .action(async ({ ctx }) => {
    try {
      const summary = await getInstagramStatisticsSummary();
      return { success: true, data: summary };
    } catch (error) {
      console.error('Error fetching Instagram statistics summary:', error);
      return {
        success: false,
        error: 'Failed to fetch statistics summary. Please try again.'
      };
    }
  });

/**
 * Get Instagram business account information
 */
export const getInstagramBusinessInfoAction = authOrganizationActionClient
  .metadata({ actionName: 'getInstagramBusinessInfo' })
  .schema(z.object({}))
  .action(async ({ ctx }) => {
    try {
      const businessInfo = await getInstagramBusinessInfo();
      return { success: true, data: businessInfo };
    } catch (error) {
      console.error('Error fetching Instagram business info:', error);
      return {
        success: false,
        error: 'Failed to fetch business account information. Please try again.'
      };
    }
  });

/**
 * Refresh Instagram statistics cache
 */
export const refreshInstagramStatisticsAction = authOrganizationActionClient
  .metadata({ actionName: 'refreshInstagramStatistics' })
  .schema(refreshStatisticsSchema)
  .action(async ({ parsedInput, ctx }) => {
    try {
      const organizationId = parsedInput.organizationId || ctx.organization.id;

      // Invalidate all statistics cache for the organization
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.InstagramStatistics,
          organizationId
        )
      );

      // Instagram settings and contacts cache removed - no invalidation needed

      return {
        success: true,
        message: 'Statistics cache refreshed successfully'
      };
    } catch (error) {
      console.error('Error refreshing Instagram statistics cache:', error);
      return {
        success: false,
        error: 'Failed to refresh statistics cache. Please try again.'
      };
    }
  });
