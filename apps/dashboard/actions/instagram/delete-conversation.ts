'use server';

import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { prisma } from '@workspace/database/client';
import { NotFoundError } from '@workspace/common/errors';

import { authOrganizationActionClient } from '~/actions/safe-action';
import {
  Caching,
  OrganizationCacheKey
} from '~/data/caching';

const deleteConversationSchema = z.object({
  contactId: z.string().uuid()
});

export const deleteConversation = authOrganizationActionClient
  .metadata({ actionName: 'deleteConversation' })
  .schema(deleteConversationSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Find the contact and make sure it belongs to the organization
    const contact = await prisma.instagramContact.findFirst({
      where: {
        id: parsedInput.contactId,
        organizationId: ctx.organization.id
      }
    });

    if (!contact) {
      throw new NotFoundError('Contact not found');
    }

    // Delete all messages for this contact
    await prisma.instagramMessage.deleteMany({
      where: {
        contactId: contact.id
      }
    });

    // Delete the contact
    await prisma.instagramContact.delete({
      where: {
        id: contact.id
      }
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramContact,
        ctx.organization.id
      )
    );

    return { success: true };
  });
