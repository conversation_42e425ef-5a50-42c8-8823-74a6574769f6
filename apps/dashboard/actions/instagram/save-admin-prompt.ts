'use server';

import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';

const saveAdminPromptSchema = z.object({
  id: z.string().uuid().optional(),
  generalPrompt: z.string().min(1, 'General prompt is required'),
  technicalPrompt: z.string().min(1, 'Technical prompt is required'),
  conversationGatheringPrompt: z.string().optional()
});

export const saveAdminPrompt = authOrganizationActionClient
  .metadata({ actionName: 'saveAdminPrompt' })
  .schema(saveAdminPromptSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Only allow SaaS admin (<EMAIL>) to save admin prompt
    if (ctx.session.user.email !== '<EMAIL>') {
      throw new Error('Only SaaS admin can save admin prompt');
    }

    if (parsedInput.id) {
      // Update existing prompt
      await prisma.adminPrompt.update({
        where: { id: parsedInput.id },
        data: {
          generalPrompt: parsedInput.generalPrompt,
          technicalPrompt: parsedInput.technicalPrompt,
          conversationGatheringPrompt: parsedInput.conversationGatheringPrompt,
          updatedAt: new Date()
        }
      });
    } else {
      // Create new prompt
      await prisma.adminPrompt.create({
        data: {
          generalPrompt: parsedInput.generalPrompt,
          technicalPrompt: parsedInput.technicalPrompt,
          conversationGatheringPrompt: parsedInput.conversationGatheringPrompt
        }
      });
    }

    // Cache invalidation removed - Admin prompt no longer cached

    return { success: true };
  });
