'use server';

import { z } from 'zod';

import { prisma } from '@workspace/database/client';

import { authOrganizationActionClient } from '~/actions/safe-action';

const saveTestAdminPromptSchema = z.object({
  id: z.string().uuid().optional(),
  generalPrompt: z.string().min(1, 'General prompt is required'),
  technicalPrompt: z.string().min(1, 'Technical prompt is required'),
  conversationGatheringPrompt: z.string().optional()
});

export const saveTestAdminPrompt = authOrganizationActionClient
  .metadata({ actionName: 'saveTestAdminPrompt' })
  .schema(saveTestAdminPromptSchema)
  .action(async ({ parsedInput, ctx }) => {
    const data = {
      organizationId: ctx.organization.id,
      userId: ctx.session.user.id,
      generalPrompt: parsedInput.generalPrompt,
      technicalPrompt: parsedInput.technicalPrompt,
      conversationGatheringPrompt: parsedInput.conversationGatheringPrompt
    };

    if (parsedInput.id) {
      // Update existing test admin prompt
      await prisma.testAdminPrompt.update({
        where: { id: parsedInput.id },
        data,
        select: { id: true }
      });
    } else {
      // Create new test admin prompt
      await prisma.testAdminPrompt.upsert({
        where: {
          organizationId_userId: {
            organizationId: ctx.organization.id,
            userId: ctx.session.user.id
          }
        },
        update: data,
        create: data,
        select: { id: true }
      });
    }

    // Cache invalidation removed - Prompt config no longer cached
  });
