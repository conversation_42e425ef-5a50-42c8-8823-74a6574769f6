-- Add field to store the actual Instagram Business Account ID that webhooks use
-- This is separate from instagramAccountId which may be the user ID from OAuth

ALTER TABLE "InstagramSettings" ADD COLUMN "instagramWebhookId" TEXT;

-- Create index for webhook lookups
CREATE INDEX "InstagramSettings_instagramWebhookId_idx" ON "InstagramSettings"("instagramWebhookId");

-- Add comment explaining the fields
COMMENT ON COLUMN "InstagramSettings"."instagramAccountId" IS 'Instagram Account ID from OAuth (may be user ID)';
COMMENT ON COLUMN "InstagramSettings"."instagramWebhookId" IS 'Instagram Business Account ID used by webhooks (learned from first webhook)';