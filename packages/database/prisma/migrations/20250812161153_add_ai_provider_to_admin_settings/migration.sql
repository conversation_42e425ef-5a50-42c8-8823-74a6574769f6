/*
  Warnings:

  - You are about to alter the column `stripeCustomerId` on the `Organization` table. The data in that column could be lost. The data in that column will be cast from `Text` to `Var<PERSON>har(255)`.

*/
-- AlterTable
ALTER TABLE "Organization" ALTER COLUMN "stripeCustomerId" DROP NOT NULL,
ALTER COLUMN "stripeCustomerId" SET DATA TYPE VARCHAR(255);

-- Add aiProvider column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'AdminSettings' AND column_name = 'aiProvider') THEN
        ALTER TABLE "AdminSettings" ADD COLUMN "aiProvider" TEXT NOT NULL DEFAULT 'claude';
    END IF;
END $$;

-- Add openrouterApiKey column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'AdminSettings' AND column_name = 'openrouterApiKey') THEN
        ALTER TABLE "AdminSettings" ADD COLUMN "openrouterApiKey" TEXT;
    END IF;
END $$;

-- Add openrouterModel column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'AdminSettings' AND column_name = 'openrouterModel') THEN
        ALTER TABLE "AdminSettings" ADD COLUMN "openrouterModel" TEXT;
    END IF;
END $$;

-- Add useReasoning column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'AdminSettings' AND column_name = 'useReasoning') THEN
        ALTER TABLE "AdminSettings" ADD COLUMN "useReasoning" BOOLEAN NOT NULL DEFAULT false;
    END IF;
END $$;

-- Add reasoningBudget column if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'AdminSettings' AND column_name = 'reasoningBudget') THEN
        ALTER TABLE "AdminSettings" ADD COLUMN "reasoningBudget" INTEGER NOT NULL DEFAULT 4000;
    END IF;
END $$;

-- Create index if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE tablename = 'AdminSettings' AND indexname = 'AdminSettings_aiProvider_idx') THEN
        CREATE INDEX "AdminSettings_aiProvider_idx" ON "AdminSettings"("aiProvider");
    END IF;
END $$;
