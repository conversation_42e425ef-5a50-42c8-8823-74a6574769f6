import axios from 'axios';
import { prisma } from '@workspace/database/client';
import type { InstagramContactStage, FollowUpStatus } from '@workspace/database';
import { keys } from '../keys';
import { invalidateInstagramContactsCache } from './cache-invalidator';
import {
  compressConversationHistory,
  createCacheOptimizedConversation,
  logCachePerformance
} from './conversation-compressor';
import { logger, createTimer, logClaudeCache } from '@workspace/common/logger';
import type { GenerateResponseOptions } from './types';
import { debugStore } from './debug-store';

// AI Orchestration system disabled

// Orchestrator system disabled - all tracking, circuit breaker, and processing state management removed

// Function to get environment variables at runtime
function getEnvironmentVariables() {
  const env = keys();
  return {
    ANTHROPIC_API_KEY: env.ANTHROPIC_API_KEY,
    CLAUDE_MODEL: env.CLAUDE_MODEL
  };
}



interface ResponseFormat {
  message: string;
  messages?: string[];
  stage?: string;
  priority?: number; // 1-5 (1=highest priority, 5=lowest priority)
  reason?: string; // Reason for stage change (required for disqualified stage)
  followUps?: {
    message: string;
    delayHours: number;
  }[];
  linkToSend?: string;
  messageDelaySeconds?: number;

  // Debug information
  thinking?: string;
  rawClaudeResponse?: unknown;

  // Original format fields
  message1?: string;
  message2?: string;
  message3?: string;
  message4?: string;
  slack_message?: string;
  fu1_message?: string;
  fu1_time?: string;
  fu1_status?: string;
  fu2_message?: string;
  fu2_time?: string;
  fu2_status?: string;
  fu3_message?: string;
  fu3_time?: string;
  fu3_status?: string;
  fu4_message?: string;
  fu4_time?: string;
  fu4_status?: string;
}

// Import conversation formatting utilities
export { formatConversationHistory } from './conversation-formatter';
export type { MessageData } from './conversation-formatter';

/**
 * Generate a response using Claude API with enhanced stage management, with optional OpenRouter support
 */
export async function generateInstagramResponse({
  prompt,
  conversationHistory,
  organizationId,
  disableCache = false
}: GenerateResponseOptions): Promise<ResponseFormat> {
  const timer = createTimer('AI Response Generation');

  logClaudeCache('Starting AI response generation', {
    operation: 'generateInstagramResponse',
    organizationId,
    conversationHistoryLength: conversationHistory.length,
    disableCache
  });

  try {
    // Get admin settings to determine AI provider configuration
    const adminSettings = await prisma.adminSettings.findFirst({
      select: {
        aiProvider: true,
        openrouterApiKey: true,
        openrouterModel: true,
        useReasoning: true,
        reasoningBudget: true,
        cacheForAllUsers: true,
        cacheType: true,
        enableConversationCache: true,
        maxConversationMessages: true
      }
    });

    // Determine which provider to use
    const aiProvider = adminSettings?.aiProvider || 'claude';
    
    // If OpenRouter is selected, use the provider abstraction system
    if (aiProvider === 'openrouter') {
      if (!adminSettings?.openrouterApiKey) {
        logger.error('OpenRouter selected but no API key configured', {
          operation: 'generateInstagramResponse',
          organizationId
        });
        throw new Error('OpenRouter API key is not configured');
      }
      
      if (!adminSettings?.openrouterModel) {
        logger.error('OpenRouter selected but no model configured', {
          operation: 'generateInstagramResponse',
          organizationId
        });
        throw new Error('OpenRouter model is not configured');
      }

      // Use OpenRouter provider
      const { AIProviderFactory } = await import('./ai-provider-interface');
      const provider = await AIProviderFactory.create({
        provider: 'openrouter' as const,
        apiKey: adminSettings.openrouterApiKey!,
        model: adminSettings.openrouterModel!,
        useReasoning: adminSettings.useReasoning || false,
        reasoningBudget: adminSettings.reasoningBudget || 4000
      });

      logger.info('Using OpenRouter AI provider', {
        operation: 'generateInstagramResponse',
        organizationId,
        provider: aiProvider,
        model: adminSettings.openrouterModel,
        useReasoning: adminSettings.useReasoning || false
      });

      const response = await provider.generateResponse({
        prompt,
        conversationHistory,
        organizationId: organizationId || '',
        disableCache
      });

      timer.end({
        organizationId,
        success: true,
        provider: aiProvider,
        model: adminSettings.openrouterModel,
        messageCount: response.messages?.length || 0,
        stage: response.stage
      });

      return response;
    }

    // Default to Claude with original comprehensive functionality
    const env = getEnvironmentVariables();

    logClaudeCache('Starting Claude AI response generation', {
      operation: 'generateInstagramResponse',
      organizationId,
      conversationHistoryLength: conversationHistory.length,
      hasAnthropicKey: !!env.ANTHROPIC_API_KEY,
      claudeModel: env.CLAUDE_MODEL,
      disableCache
    });

    if (!env.ANTHROPIC_API_KEY) {
      logger.error('ANTHROPIC_API_KEY is not configured', {
        operation: 'generateInstagramResponse',
        organizationId
      });
      throw new Error('ANTHROPIC_API_KEY is not configured');
    }

    // Disable cache if explicitly requested (e.g., for test environments)
    const shouldUseCache = !disableCache && (adminSettings?.cacheForAllUsers || false);
    const cacheType = (adminSettings?.cacheType as '5m' | '1h') || '5m';
    const enableConversationCache = adminSettings?.enableConversationCache ?? true;
    const maxConversationMessages = adminSettings?.maxConversationMessages || 200;

    logClaudeCache('Retrieved admin cache settings', {
      operation: 'getCacheSettings',
      organizationId,
      shouldUseCache,
      cacheType,
      enableConversationCache,
      maxConversationMessages,
      disableCache,
      cacheDisabledReason: disableCache ? 'Test environment' : undefined
    });

    // Check if this is a conversation gathering scenario (no new user message)
    const isConversationGathering = prompt.includes('CONVERSATION GATHERING') ||
      (organizationId && organizationId.includes('gathering')) ||
      prompt.includes('user has NOT sent us a new message');

    // Use the enhanced prompt builder if organizationId is provided
    let systemPrompt: string;
    if (organizationId) {
      try {
        if (isConversationGathering) {
          const { buildConversationGatheringPrompt } = await import('./prompt-builder');
          systemPrompt = await buildConversationGatheringPrompt(organizationId);
          console.log('Using conversation gathering prompt for organization:', organizationId);
        } else {
          const { buildFullPrompt } = await import('./prompt-builder');
          systemPrompt = await buildFullPrompt(organizationId);
          console.log('Using enhanced prompt builder for organization:', organizationId);
        }
      } catch (error) {
        console.warn('Failed to build enhanced prompt, falling back to basic prompt:', error);
        systemPrompt = prompt;
      }
    } else {
      systemPrompt = prompt;
    }

    // Add conversation context for normal conversations only
    // Conversation gathering mode gets all its instructions from the admin prompt
    if (!isConversationGathering) {
      systemPrompt += `

You are an Instagram DM bot that helps convert leads into appointments.
You should respond as "Ja" in the conversation.

The conversation history is formatted as:
User: [user message]
Ja: [your previous response]
User: [user message]
...

NORMAL CONVERSATION: The user has sent you a new message and expects an immediate response.
- Provide immediate response messages (message1, message2, etc)
- Also set follow-up messages if appropriate

CRITICAL: You must ALWAYS respond with valid JSON in this exact format:
{
  "stage": "new|initial|engaged|qualified|formsent|disqualified|converted",
  "priority": 1-5,
  ${isConversationGathering ? `
  "message1": null,
  "message2": null,
  "message3": null,
  "message4": null,` : `
  "message1": "Your response message",
  "message2": "Optional second message",
  "message3": "Optional third message",
  "message4": "Optional fourth message",`}
  "fu1_message": "Optional follow-up message",
  "fu1_time": "2025-06-01T12:00:00+01:00",
  "fu1_status": "pending",
  "fu2_message": "Optional second follow-up message",
  "fu2_time": "2025-06-02T12:00:00+01:00",
  "fu2_status": "pending"
}

Never respond with plain text. Always return valid JSON even for old conversations.`;

      // Log the generated system prompt for debugging
      logClaudeCache('Generated system prompt', {
        operation: 'systemPromptGeneration',
        organizationId,
        systemPromptLength: systemPrompt.length,
        systemPromptPreview: systemPrompt.substring(0, 200) + '...'
      });

      // Log the conversation history for debugging
      logClaudeCache('Generated conversation history', {
        operation: 'conversationHistoryGeneration',
        organizationId,
        conversationHistoryLength: conversationHistory.length,
        conversationHistoryPreview: conversationHistory.substring(0, 500) + '...',
        fullConversationHistory: conversationHistory
      });

      let systemMessage: string | any[];
      let userMessage: string;
      let cacheInfo = { systemCached: false, conversationCached: false, estimatedTokens: 0 };

      if (shouldUseCache) {
        const cacheTimer = createTimer('Claude Cache - Conversation Processing');

        logClaudeCache('Starting conversation compression for caching', {
          operation: 'conversationCompression',
          organizationId,
          originalLength: conversationHistory.length,
          maxConversationMessages
        });

        // Parse conversation history for compression
        const messages = conversationHistory.split('\n').map(line => {
          const isFromUser = line.startsWith('User:');
          const content = line.replace(/^(User|Ja):\s*/, '');
          return {
            isFromUser,
            content,
            mediaDescription: null,
            mediaUrl: null,
            mediaType: null
          };
        }).filter(msg => msg.content.trim().length > 0);

        // Compress conversation history
        const compressed = compressConversationHistory(messages, maxConversationMessages);

        logClaudeCache('Conversation compression completed', {
          operation: 'conversationCompression',
          organizationId,
          originalMessages: messages.length,
          compressedMessages: compressed.messageCount,
          compressionRatio: parseFloat(compressed.compressionRatio.toFixed(2)),
          estimatedTokens: compressed.tokenEstimate
        });

        // Create cache-optimized conversation
        const optimized = createCacheOptimizedConversation(
          systemPrompt,
          compressed.compressedHistory,
          cacheType,
          enableConversationCache
        );

        systemMessage = optimized.systemMessage;
        userMessage = optimized.userMessage;
        cacheInfo = optimized.cacheInfo;

        logClaudeCache('Cache optimization completed', {
          operation: 'cacheOptimization',
          organizationId,
          cacheType,
          systemCached: cacheInfo.systemCached,
          conversationCached: cacheInfo.conversationCached,
          estimatedTokens: cacheInfo.estimatedTokens,
          enableConversationCache
        });

        cacheTimer.end({
          organizationId,
          compressionRatio: compressed.compressionRatio,
          systemCached: cacheInfo.systemCached,
          conversationCached: cacheInfo.conversationCached
        });
      } else {
        // No caching - use simple format
        systemMessage = systemPrompt;
        userMessage = `Here is the conversation history:\n\n${conversationHistory}\n\nPlease respond to the user's last message.`;

        logClaudeCache('Skipping cache - using simple format', {
          operation: 'noCaching',
          organizationId,
          shouldUseCache: false
        });
      }

      const requestBody = {
        model: env.CLAUDE_MODEL,
        max_tokens: 10000,
        system: systemMessage,
        messages: [
          {
            role: 'user',
            content: userMessage
          }
        ],
        ...(adminSettings?.useReasoning && {
          thinking: {
            type: "enabled" as const,
            budget_tokens: adminSettings?.reasoningBudget || 4000
          }
        })
      };

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'x-api-key': env.ANTHROPIC_API_KEY,
        'anthropic-version': '2023-06-01'
      };

      // Add beta headers for prompt caching if needed
      if (shouldUseCache) {
        headers['anthropic-beta'] = 'prompt-caching-2024-07-31';

        // Add 1-hour cache beta header if using 1h cache
        if (cacheType === '1h') {
          headers['anthropic-beta'] += ',extended-cache-ttl-2025-04-11';
        }
      }

      // Log full request details
      console.log('=== FULL CLAUDE REQUEST ===');
      console.log('Headers:', JSON.stringify(headers, null, 2));
      console.log('Request Body:', JSON.stringify(requestBody, null, 2));
      console.log('=== END CLAUDE REQUEST ===');

      logClaudeCache('Making request to Anthropic API', {
        operation: 'anthropicAPIRequest',
        organizationId,
        model: requestBody.model,
        hasSystemMessage: !!requestBody.system,
        messageCount: requestBody.messages.length,
        maxTokens: requestBody.max_tokens,
        thinkingEnabled: !!requestBody.thinking,
        thinkingBudget: requestBody.thinking ? requestBody.thinking.budget_tokens : 0,
        betaHeaders: headers['anthropic-beta'] || 'none',
        cacheType: shouldUseCache ? cacheType : undefined,
        systemCached: cacheInfo.systemCached,
        conversationCached: cacheInfo.conversationCached,
        estimatedTokens: cacheInfo.estimatedTokens,
        systemMessageLength: typeof requestBody.system === 'string' ? requestBody.system.length : 0,
        userMessageLength: requestBody.messages[0]?.content?.length || 0
      });

      const apiTimer = createTimer('Anthropic API Request');
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        requestBody,
        { headers }
      );

      // Log full response details
      console.log('=== FULL CLAUDE RESPONSE ===');
      console.log('Response Status:', response.status);
      console.log('Response Headers:', JSON.stringify(response.headers, null, 2));
      console.log('Response Data:', JSON.stringify(response.data, null, 2));
      console.log('=== END CLAUDE RESPONSE ===');

      // Check for Claude 4 refusal stop reason
      if (response.data.stop_reason === 'refusal') {
        logger.warn('Claude 4 refused to generate content for safety reasons', {
          operation: 'anthropicAPIRequest',
          organizationId,
          stopReason: 'refusal'
        });
        throw new Error('Claude refused to generate content for safety reasons. Please modify your prompt.');
      }

      // Log cache performance if available
      if (response.data.usage && shouldUseCache) {
        logCachePerformance(response.data.usage, cacheInfo);

        logClaudeCache('Cache performance metrics', {
          operation: 'cachePerformance',
          organizationId,
          cacheCreationTokens: response.data.usage.cache_creation_input_tokens || 0,
          cacheReadTokens: response.data.usage.cache_read_input_tokens || 0,
          inputTokens: response.data.usage.input_tokens || 0,
          outputTokens: response.data.usage.output_tokens || 0,
          cacheHit: (response.data.usage.cache_read_input_tokens || 0) > 0
        });
      }

      apiTimer.end({
        organizationId,
        model: requestBody.model,
        inputTokens: response.data.usage?.input_tokens || 0,
        outputTokens: response.data.usage?.output_tokens || 0,
        cacheHit: shouldUseCache && (response.data.usage?.cache_read_input_tokens || 0) > 0
      });

      // Extract the text content and thinking from the response
      const textContent = response.data.content.find(
        (block: { type: string; text?: string }) => block.type === 'text'
      )?.text || '';

      // Extract thinking if available (Claude 4 feature)
      const thinkingContent = response.data.content.find(
        (block: { type: string; thinking?: string }) => block.type === 'thinking'
      )?.thinking || null;

      // Log cache performance if caching was used
      if (shouldUseCache && response.data.usage) {
        logCachePerformance(response.data.usage, cacheInfo);
      }

      // Parse the JSON response
      try {
        // Find JSON in the response (it might be wrapped in markdown code blocks)
        // Try to extract JSON from the response
        let jsonStr = '';

        // First try to extract JSON from code blocks
        const jsonBlockMatch = textContent.match(/```json\n([\s\S]*?)\n```/) || textContent.match(/```\n([\s\S]*?)\n```/);
        if (jsonBlockMatch && jsonBlockMatch[1]) {
          jsonStr = jsonBlockMatch[1];
        }
        // If no code blocks, try to find JSON directly
        else {
          const jsonMatch = textContent.match(/{[\s\S]*?}/);
          if (jsonMatch) {
            jsonStr = jsonMatch[0];
          }
          // If still no match, use the entire text
          else {
            jsonStr = textContent;
          }
        }

        // Clean up any remaining markdown formatting and normalize the JSON
        jsonStr = jsonStr
          .replace(/```json\n|```\n|```/g, '') // Remove markdown
          .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Replace „text" with \"text\"
          .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Handle different quote combinations
          .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes with regular quotes
          .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
          .replace(/\u2026/g, '...') // Replace ellipsis character
          .trim();

        console.log('Attempting to parse JSON:', jsonStr.substring(0, 200) + '...');
        const parsedResponse = JSON.parse(jsonStr);

        console.log('Successfully parsed AI response. Stage:', parsedResponse.stage, 'Messages found:',
          parsedResponse.messages?.length ||
          [parsedResponse.message1, parsedResponse.message2, parsedResponse.message3, parsedResponse.message4].filter(Boolean).length);

        // Handle all possible formats
        let messages: string[] = [];

        // New format with messages array
        if (parsedResponse.messages && Array.isArray(parsedResponse.messages)) {
          messages = parsedResponse.messages;
        }
        // Single message format
        else if (parsedResponse.message) {
          messages = [parsedResponse.message];
        }
        // Original format with message1, message2, etc.
        else {
          // Add messages from message1-4 fields if they exist
          if (parsedResponse.message1) messages.push(parsedResponse.message1);
          if (parsedResponse.message2) messages.push(parsedResponse.message2);
          if (parsedResponse.message3) messages.push(parsedResponse.message3);
          if (parsedResponse.message4) messages.push(parsedResponse.message4);
        }

        // Handle follow-ups
        const followUps = parsedResponse.followUps || [];

        // Convert original format follow-ups to new format if needed
        if (!followUps.length) {
          if (parsedResponse.fu1_message) {
            followUps.push({
              message: parsedResponse.fu1_message,
              delayHours: 24 // Default delay
            });
          }
          if (parsedResponse.fu2_message) {
            followUps.push({
              message: parsedResponse.fu2_message,
              delayHours: 48 // Default delay
            });
          }
          if (parsedResponse.fu3_message) {
            followUps.push({
              message: parsedResponse.fu3_message,
              delayHours: 72 // Default delay
            });
          }
          if (parsedResponse.fu4_message) {
            followUps.push({
              message: parsedResponse.fu4_message,
              delayHours: 96 // Default delay
            });
          }
        }

        const result = {
          // Required field
          message: messages[0] || '',

          // New format fields
          messages: messages,
          stage: parsedResponse.stage || 'initial',
          priority: parsedResponse.priority || 3, // Default to medium priority
          reason: parsedResponse.reason,
          followUps: followUps,
          linkToSend: parsedResponse.linkToSend,
          messageDelaySeconds: parsedResponse.messageDelaySeconds || 3,

          // Debug information
          thinking: thinkingContent,
          rawClaudeResponse: response.data,

          // Original format fields
          message1: parsedResponse.message1,
          message2: parsedResponse.message2,
          message3: parsedResponse.message3,
          message4: parsedResponse.message4,
          slack_message: parsedResponse.slack_message,
          fu1_message: parsedResponse.fu1_message,
          fu1_time: parsedResponse.fu1_time,
          fu1_status: parsedResponse.fu1_status,
          fu2_message: parsedResponse.fu2_message,
          fu2_time: parsedResponse.fu2_time,
          fu2_status: parsedResponse.fu2_status,
          fu3_message: parsedResponse.fu3_message,
          fu3_time: parsedResponse.fu3_time,
          fu3_status: parsedResponse.fu3_status,
          fu4_message: parsedResponse.fu4_message,
          fu4_time: parsedResponse.fu4_time,
          fu4_status: parsedResponse.fu4_status
        };

        // Orchestration logging removed

        logClaudeCache('AI response generation completed successfully', {
          operation: 'generateInstagramResponse',
          organizationId,
          messageCount: messages.length,
          stage: result.stage,
          followUpCount: followUps.length,
          hasLinkToSend: !!result.linkToSend
        });

        timer.end({
          organizationId,
          success: true,
          messageCount: messages.length,
          stage: result.stage
        });

        return result;
      } catch (parseError) {
        console.error('Error parsing AI response:', parseError);
        console.error('Raw text content:', textContent);

        // Enhanced fallback: Try to extract JSON more aggressively
        let fallbackMessage = 'Sorry, I encountered an error processing your message. Please try again.';

        try {
          // Try to find and parse JSON even if it's malformed
          const jsonMatch = textContent.match(/{[\s\S]*}/);
          if (jsonMatch) {
            const jsonStr = jsonMatch[0];

            // Try to fix common JSON issues
            const fixedJson = jsonStr
              .replace(/```json\n|```\n|```/g, '') // Remove markdown
              .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Replace „text" with \"text\"
              .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Handle different quote combinations
              .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes with regular quotes
              .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
              .replace(/\u2026/g, '...') // Replace ellipsis character
              .replace(/\n\s*/g, ' ') // Remove newlines and extra spaces
              .replace(/,\s*}/g, '}') // Remove trailing commas
              .replace(/,\s*]/g, ']'); // Remove trailing commas in arrays

            const parsedFallback = JSON.parse(fixedJson);

            // Extract messages from the parsed fallback
            if (parsedFallback.message1) {
              fallbackMessage = parsedFallback.message1;
            } else if (parsedFallback.message) {
              fallbackMessage = parsedFallback.message;
            }
          }
        } catch (fallbackParseError) {
          console.error('Fallback JSON parsing also failed:', fallbackParseError);
          // Use the default error message
        }

        return {
          message: fallbackMessage,
          messages: [fallbackMessage],
          message1: fallbackMessage,
          stage: 'initial',
          messageDelaySeconds: 3,
          thinking: thinkingContent,
          rawClaudeResponse: response.data
        };
      }
    } else {
      // Conversation gathering mode - use simple format without caching
      const userMessage = `Here is the conversation history:\n\n${conversationHistory}\n\nAnalyze this conversation and provide follow-up strategy.`;

      const requestBody = {
        model: env.CLAUDE_MODEL,
        max_tokens: 10000,
        system: systemPrompt,
        messages: [
          {
            role: 'user',
            content: userMessage
          }
        ],
        ...(adminSettings?.useReasoning && {
          thinking: {
            type: "enabled" as const,
            budget_tokens: adminSettings?.reasoningBudget || 4000
          }
        })
      };

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'x-api-key': env.ANTHROPIC_API_KEY,
        'anthropic-version': '2023-06-01'
      };

      console.log('=== CONVERSATION GATHERING CLAUDE REQUEST ===');
      console.log('Headers:', JSON.stringify(headers, null, 2));
      console.log('Request Body:', JSON.stringify(requestBody, null, 2));
      console.log('=== END CONVERSATION GATHERING REQUEST ===');

      const apiTimer = createTimer('Anthropic API Request - Conversation Gathering');
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        requestBody,
        { headers }
      );

      console.log('=== CONVERSATION GATHERING CLAUDE RESPONSE ===');
      console.log('Response Status:', response.status);
      console.log('Response Data:', JSON.stringify(response.data, null, 2));
      console.log('=== END CONVERSATION GATHERING RESPONSE ===');

      // Extract the text content and thinking from the response
      const textContent = response.data.content.find(
        (block: { type: string; text?: string }) => block.type === 'text'
      )?.text || '';

      // Extract thinking if available (Claude 4 feature)
      const thinkingContent = response.data.content.find(
        (block: { type: string; thinking?: string }) => block.type === 'thinking'
      )?.thinking || null;

      // Parse the JSON response
      try {
        let jsonStr = '';

        // First try to extract JSON from code blocks
        const jsonBlockMatch = textContent.match(/```json\n([\s\S]*?)\n```/) || textContent.match(/```\n([\s\S]*?)\n```/);
        if (jsonBlockMatch && jsonBlockMatch[1]) {
          jsonStr = jsonBlockMatch[1];
        }
        // If no code blocks, try to find JSON directly
        else {
          const jsonMatch = textContent.match(/{[\s\S]*?}/);
          if (jsonMatch) {
            jsonStr = jsonMatch[0];
          }
          // If still no match, use the entire text
          else {
            jsonStr = textContent;
          }
        }

        // Clean up any remaining markdown formatting and normalize the JSON
        jsonStr = jsonStr
          .replace(/```json\n|```\n|```/g, '') // Remove markdown
          .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Replace „text" with \"text\"
          .replace(/„([^"]*?)"/g, '\\"$1\\"')  // Handle different quote combinations
          .replace(/[\u201C\u201D]/g, '"') // Replace smart quotes with regular quotes
          .replace(/[\u2018\u2019]/g, "'") // Replace smart single quotes
          .replace(/\u2026/g, '...') // Replace ellipsis character
          .trim();

        console.log('Attempting to parse conversation gathering JSON:', jsonStr.substring(0, 200) + '...');
        const parsedResponse = JSON.parse(jsonStr);

        console.log('Successfully parsed conversation gathering AI response. Stage:', parsedResponse.stage, 'Priority:', parsedResponse.priority);

        // Handle follow-ups
        const followUps = [];
        if (parsedResponse.fu1_message) {
          followUps.push({
            message: parsedResponse.fu1_message,
            delayHours: 24 // Default delay
          });
        }
        if (parsedResponse.fu2_message) {
          followUps.push({
            message: parsedResponse.fu2_message,
            delayHours: 48 // Default delay
          });
        }

        const result = {
          // Required field
          message: '', // No immediate message for conversation gathering

          // New format fields
          messages: [], // No immediate messages for conversation gathering
          stage: parsedResponse.stage || 'initial',
          priority: parsedResponse.priority || 3,
          reason: parsedResponse.reason,
          followUps: followUps,
          messageDelaySeconds: 0, // No delay needed for conversation gathering

          // Debug information
          thinking: thinkingContent,
          rawClaudeResponse: response.data,

          // Original format fields
          message1: undefined,
          message2: undefined,
          message3: undefined,
          message4: undefined,
          fu1_message: parsedResponse.fu1_message,
          fu1_time: parsedResponse.fu1_time,
          fu1_status: parsedResponse.fu1_status,
          fu2_message: parsedResponse.fu2_message,
          fu2_time: parsedResponse.fu2_time,
          fu2_status: parsedResponse.fu2_status,
          fu3_message: parsedResponse.fu3_message,
          fu3_time: parsedResponse.fu3_time,
          fu3_status: parsedResponse.fu3_status,
          fu4_message: parsedResponse.fu4_message,
          fu4_time: parsedResponse.fu4_time,
          fu4_status: parsedResponse.fu4_status
        };

        apiTimer.end({
          organizationId,
          model: requestBody.model,
          inputTokens: response.data.usage?.input_tokens || 0,
          outputTokens: response.data.usage?.output_tokens || 0,
          cacheHit: false
        });

        timer.end({
          organizationId,
          success: true,
          messageCount: 0,
          stage: result.stage
        });

        return result;
      } catch (parseError) {
        console.error('Error parsing conversation gathering AI response:', parseError);
        console.error('Raw text content:', textContent);

        return {
          message: '',
          messages: [],
          message1: undefined,
          message2: undefined,
          message3: undefined,
          message4: undefined,
          stage: 'initial',
          priority: 3,
          messageDelaySeconds: 0,
          thinking: thinkingContent,
          rawClaudeResponse: response.data
        };
      }
    }
  } catch (error) {
    logger.error('Error generating AI response', {
      operation: 'generateInstagramResponse',
      organizationId
    }, error as Error);

    // Provide more specific error information
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const statusText = error.response?.statusText;
      const responseData = error.response?.data;

      logClaudeCache('Anthropic API error', {
        operation: 'anthropicAPIError',
        organizationId,
        status,
        statusText,
        errorMessage: error.message,
        responseData: responseData ? JSON.stringify(responseData).substring(0, 500) : undefined
      });

      if (status === 401) {
        throw new Error('Authentication failed: Invalid or missing ANTHROPIC_API_KEY. Please check your environment variables.');
      } else if (status === 400) {
        throw new Error(`Bad request to Anthropic API: ${responseData?.error?.message || error.message}`);
      } else if (status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      } else if (status === 500) {
        throw new Error('Anthropic API server error. Please try again later.');
      } else {
        throw new Error(`Anthropic API error (${status}): ${statusText || error.message}`);
      }
    }

    timer.end({
      organizationId,
      success: false,
      error: true
    });

    throw new Error(`Failed to generate AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Process the AI response and update the database
 */
export async function processResponse(
  contactId: string,
  response: ResponseFormat,
  _instagramToken?: string,
  sendMessageFunction?: (contactId: string, message: string) => Promise<any>,
  settings?: { messageDelayMin: number; messageDelayMax: number },
  conversationHistory?: string
): Promise<void> {
  try {
    // Get the delay between messages (in milliseconds)
    let messageDelay: number;

    if (settings) {
      // Use a random delay between min and max from settings
      const min = settings.messageDelayMin * 1000;
      const max = settings.messageDelayMax * 1000;
      messageDelay = Math.floor(Math.random() * (max - min + 1)) + min;
    } else {
      // Use the delay from the response or default to 3 seconds
      messageDelay = (response.messageDelaySeconds || 3) * 1000;
    }

    // Determine which messages to send
    let messagesToSend: string[] = [];

    // If we have messages array, use it
    if (response.messages && response.messages.length > 0) {
      messagesToSend = response.messages.filter(msg => msg && typeof msg === 'string' && msg.trim().length > 0);
    }
    // Otherwise, use message1-4 fields
    else {
      if (response.message1 && typeof response.message1 === 'string' && response.message1.trim() !== 'null') {
        messagesToSend.push(response.message1);
      }
      if (response.message2 && typeof response.message2 === 'string' && response.message2.trim() !== 'null') {
        messagesToSend.push(response.message2);
      }
      if (response.message3 && typeof response.message3 === 'string' && response.message3.trim() !== 'null') {
        messagesToSend.push(response.message3);
      }
      if (response.message4 && typeof response.message4 === 'string' && response.message4.trim() !== 'null') {
        messagesToSend.push(response.message4);
      }
    }

    // Check if this is a conversation gathering scenario (no immediate messages should be sent)
    const isConversationGathering = conversationHistory?.includes('CONVERSATION GATHERING') ||
      messagesToSend.length === 0;

    // For conversation gathering, we don't send immediate messages
    if (isConversationGathering && messagesToSend.length === 0) {
      console.log('Conversation gathering scenario detected - no immediate messages to send, only follow-ups');
    } else if (messagesToSend.length === 0) {
      console.error('No valid messages found in response:', response);
      messagesToSend = ['Sorry, I encountered an error processing your message. Please try again.'];
    }

    // Validate each message to ensure it's not raw JSON
    messagesToSend = messagesToSend.map(message => {
      // Check if the message looks like raw JSON
      if (message.trim().startsWith('{') && message.trim().endsWith('}')) {
        console.error('Detected raw JSON being sent as message:', message.substring(0, 100) + '...');
        return 'Sorry, I encountered an error processing your message. Please try again.';
      }
      return message;
    });

    // Process each message with a delay (only if we have messages to send)
    if (messagesToSend.length > 0) {
      for (let i = 0; i < messagesToSend.length; i++) {
        const message = messagesToSend[i];

        // Add delay between messages (except for the first one)
        if (i > 0) {
          console.log(`Waiting ${messageDelay / 1000} seconds before sending next message...`);
          await new Promise(resolve => setTimeout(resolve, messageDelay));
        }

        console.log(`Sending message ${i + 1}/${messagesToSend.length}: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`);

        // Send the message and get the Instagram message ID
        let instagramMessageId: string | null = null;
        if (sendMessageFunction) {
          try {
            const result = await sendMessageFunction(contactId, message);
            // If the send function returns the message ID, use it
            if (result && typeof result === 'object' && 'message_id' in result) {
              instagramMessageId = result.message_id as string;
            }
          } catch (sendError) {
            console.error(`Failed to send message ${i + 1}/${messagesToSend.length}:`, sendError);
            console.log('Continuing with next message if available...');
            // Continue with the next message instead of stopping the entire process
            continue;
          }
        }

        // Store debug information with Instagram message ID if we have it
        if (instagramMessageId && i === 0 && (response.thinking || response.rawClaudeResponse)) {
          debugStore.set(instagramMessageId, {
            thinking: response.thinking,
            fullPrompt: conversationHistory ? `System: ${conversationHistory}` : undefined,
            conversationHistory: conversationHistory,
            rawResponse: response.rawClaudeResponse
          });
          console.log(`Stored debug info for Instagram message ID: ${instagramMessageId}`);
        }

        // Update message count
        const updatedContact = await prisma.instagramContact.update({
          where: { id: contactId },
          data: {
            messageCount: { increment: 1 },
            updatedAt: new Date()
          },
          select: { organizationId: true }
        });

        // Invalidate cache after message count update (only for the last message to avoid spam)
        if (i === messagesToSend.length - 1) {
          await invalidateInstagramContactsCache(updatedContact.organizationId);
        }
      }
    } else {
      console.log('No immediate messages to send - this is a conversation gathering scenario focused on follow-ups only');
    }

    // Update contact stage if provided using enhanced stage management
    if (response.stage) {
      try {
        const { changeContactStage } = await import('./stage-manager');

        // Ensure reason is provided for disqualified stage
        let reason = response.reason;
        if (response.stage === 'disqualified' && (!reason || reason.trim().length === 0)) {
          reason = 'AI determined contact does not meet qualification criteria';
        }

        const result = await changeContactStage({
          contactId,
          newStage: response.stage as InstagramContactStage,
          reason,
          changedBy: 'ai',
          metadata: {
            aiResponse: true,
            messageCount: messagesToSend.length,
            timestamp: new Date().toISOString()
          }
        });

        if (!result.success) {
          console.error('Failed to change stage:', result.error);
          // Fallback to direct update
          await prisma.instagramContact.update({
            where: { id: contactId },
            data: { stage: response.stage as InstagramContactStage }
          });
        } else if (result.shouldStop) {
          console.log(`Contact ${contactId} reached terminal stage ${response.stage}, stopping further processing`);
        }

        // Invalidate cache after stage change
        const contact = await prisma.instagramContact.findUnique({
          where: { id: contactId },
          select: { organizationId: true }
        });
        if (contact) {
          await invalidateInstagramContactsCache(contact.organizationId);
        }
      } catch (error) {
        console.error('Error with enhanced stage management, falling back to direct update:', error);
        await prisma.instagramContact.update({
          where: { id: contactId },
          data: { stage: response.stage as InstagramContactStage }
        });

        // Invalidate cache after fallback stage change
        const contact = await prisma.instagramContact.findUnique({
          where: { id: contactId },
          select: { organizationId: true }
        });
        if (contact) {
          await invalidateInstagramContactsCache(contact.organizationId);
        }
      }
    }

    // If we have a token and the response includes a link, mark as link sent
    // Note: isLinkSent field needs to be added to InstagramContact model
    // if (instagramToken && response.linkToSend) {
    //   await prisma.instagramContact.update({
    //     where: { id: contactId },
    //     data: { isLinkSent: true }
    //   });
    // }

    console.log(`Processed ${messagesToSend.length} AI response messages for contact ${contactId}`);

    // AI Orchestration Logging - Track when response processing completes
    console.log(`[AI_ORCHESTRATION] ProcessResponse completed for contact ${contactId}. Messages sent: ${messagesToSend.length}, Stage: ${response.stage}`);
    console.log(`[AI_ORCHESTRATION] *** END OF AI RESPONSE PROCESSING CYCLE ***`);
  } catch (error) {
    console.error('Error processing AI response:', error);
    console.log(`[AI_ORCHESTRATION] ProcessResponse FAILED for contact ${contactId}: ${error}`);
    throw new Error('Failed to process AI response');
  }
}

/**
 * Schedule follow-up messages if provided in the response
 */
export async function scheduleFollowUps(
  contactId: string,
  response: ResponseFormat
): Promise<void> {
  try {
    const contact = await prisma.instagramContact.findUnique({
      where: { id: contactId },
      select: { id: true, stage: true, lastInteractionAt: true }
    });

    if (!contact) {
      throw new Error('Contact not found');
    }

    // Check if contact is in a terminal stage - don't schedule follow-ups
    const terminalStages = ['converted', 'disqualified', 'blocked', 'suspicious'];
    if (terminalStages.includes(contact.stage)) {
      console.log(`Contact ${contactId} is in terminal stage ${contact.stage}, skipping follow-up scheduling`);
      return;
    }

    // Handle both formats
    if (response.followUps && response.followUps.length > 0) {
      // New format with followUps array
      // Process follow-ups (up to 6)
      const followUps = response.followUps.slice(0, 6);

      // Clear existing follow-ups for this contact (including Chrome extension batch follow-ups)
      await prisma.instagramFollowUp.deleteMany({
        where: { contactId }
      });

      // Clear Chrome extension batch message status when AI takes over
      await prisma.instagramContact.update({
        where: { id: contactId },
        data: {
          batchMessageStatus: null,
          currentMessageSequence: null,
          // AI takes over with higher priority and proper stage
          priority: response.priority || 4, // AI responses typically get higher priority
          stage: response.stage as any || 'engaged',
          lastInteractionAt: new Date(), // Update interaction time when AI responds
          updatedAt: new Date()
        }
      });

      // Import 24-hour window utilities
      const { isWithin24HourWindow, getLatest24HourTime } = await import('./24-hour-window');

      // Calculate the latest time for 24-hour window
      const latest24HourTime = getLatest24HourTime(contact.lastInteractionAt);

      // Create new follow-ups with 24-hour window validation
      const followUpData = followUps.map((followUp, index) => {
        const delayHours = followUp.delayHours || (24 * (index + 1)); // Default to 24h, 48h, 72h, etc.
        const scheduledTime = new Date();
        scheduledTime.setHours(scheduledTime.getHours() + delayHours);

        // Check if this follow-up is within the 24-hour window
        const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, scheduledTime);

        if (!isWithinWindow && latest24HourTime) {
          console.log(`Follow-up ${index + 1} scheduled for ${scheduledTime.toISOString()} is outside 24-hour window (ends at ${latest24HourTime.toISOString()})`);
        }

        return {
          contactId,
          sequenceNumber: index + 1,
          message: followUp.message,
          scheduledTime,
          status: 'pending'
        };
      });

      for (const followUp of followUpData) {
        await prisma.instagramFollowUp.create({
          data: {
            contactId: followUp.contactId,
            sequenceNumber: followUp.sequenceNumber,
            message: followUp.message,
            scheduledTime: followUp.scheduledTime,
            status: followUp.status as FollowUpStatus
          } as any
        });
      }

      // Update attack list status and next message time for AI follow-ups
      const earliestFollowUp = followUpData.reduce((earliest, current) =>
        current.scheduledTime < earliest.scheduledTime ? current : earliest
      );

      await prisma.instagramContact.update({
        where: { id: contactId },
        data: {
          attackListStatus: 'pending',
          nextMessageAt: earliestFollowUp.scheduledTime,
          updatedAt: new Date()
        }
      });

      console.log(`Scheduled ${followUps.length} follow-ups for contact ${contactId}`);
    } else {
      // Original format with fu1_message, fu1_time, etc.
      const followUpMessages = [];

      // Collect follow-up messages from the original format
      if (response.fu1_message) {
        const scheduledTime = response.fu1_time ? new Date(response.fu1_time) : new Date(Date.now() + 24 * 60 * 60 * 1000);
        followUpMessages.push({
          contactId,
          sequenceNumber: 1,
          message: response.fu1_message,
          scheduledTime,
          status: 'pending'
        });
      }

      if (response.fu2_message) {
        const scheduledTime = response.fu2_time ? new Date(response.fu2_time) : new Date(Date.now() + 48 * 60 * 60 * 1000);
        followUpMessages.push({
          contactId,
          sequenceNumber: 2,
          message: response.fu2_message,
          scheduledTime,
          status: 'pending'
        });
      }

      if (response.fu3_message) {
        const scheduledTime = response.fu3_time ? new Date(response.fu3_time) : new Date(Date.now() + 72 * 60 * 60 * 1000);
        followUpMessages.push({
          contactId,
          sequenceNumber: 3,
          message: response.fu3_message,
          scheduledTime,
          status: 'pending'
        });
      }

      if (response.fu4_message) {
        const scheduledTime = response.fu4_time ? new Date(response.fu4_time) : new Date(Date.now() + 96 * 60 * 60 * 1000);
        followUpMessages.push({
          contactId,
          sequenceNumber: 4,
          message: response.fu4_message,
          scheduledTime,
          status: 'pending'
        });
      }

      if (followUpMessages.length > 0) {
        // Clear existing follow-ups for this contact (including Chrome extension batch follow-ups)
        await prisma.instagramFollowUp.deleteMany({
          where: { contactId }
        });

        // Clear Chrome extension batch message status when AI takes over
        await prisma.instagramContact.update({
          where: { id: contactId },
          data: {
            batchMessageStatus: null,
            currentMessageSequence: null,
            // AI takes over with higher priority and proper stage
            priority: response.priority || 4, // AI responses typically get higher priority
            stage: response.stage as any || 'engaged',
            lastInteractionAt: new Date(), // Update interaction time when AI responds
            updatedAt: new Date()
          }
        });

        // Create new follow-ups
        for (const followUp of followUpMessages) {
          await prisma.instagramFollowUp.create({
            data: {
              contactId: followUp.contactId,
              sequenceNumber: followUp.sequenceNumber,
              message: followUp.message,
              scheduledTime: followUp.scheduledTime,
              status: followUp.status as FollowUpStatus
            } as any
          });
        }

        // Update attack list status and next message time for AI follow-ups
        const earliestFollowUp = followUpMessages.reduce((earliest, current) =>
          current.scheduledTime < earliest.scheduledTime ? current : earliest
        );

        await prisma.instagramContact.update({
          where: { id: contactId },
          data: {
            attackListStatus: 'pending',
            nextMessageAt: earliestFollowUp.scheduledTime,
            updatedAt: new Date()
          }
        });

        console.log(`Scheduled ${followUpMessages.length} follow-ups for contact ${contactId} (using legacy format)`);
      }
    }
  } catch (error) {
    console.error('Error scheduling follow-ups:', error);
    throw new Error('Failed to schedule follow-ups');
  }
}

/**
 * Update contact stage based on AI response
 */
export async function updateContactStage(
  contactId: string,
  stage?: string
): Promise<void> {
  if (!stage) {
    return;
  }

  try {
    await prisma.instagramContact.update({
      where: { id: contactId },
      data: { stage: stage as InstagramContactStage }
    });

    console.log(`Updated stage to "${stage}" for contact ${contactId}`);
  } catch (error) {
    console.error('Error updating contact stage:', error);
    throw new Error('Failed to update contact stage');
  }
}
