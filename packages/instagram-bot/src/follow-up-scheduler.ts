import { prisma } from '@workspace/database/client';
import { FollowUpStatus } from '@workspace/database';
import { BotResponse } from './types';
import { isWithin24HourWindow, getLatest24HourTime } from './24-hour-window';

/**
 * LEGACY FOLLOW-UP SCHEDULER
 * This is legacy code that uses old database schema fields.
 * The new enhanced follow-up system is in ai-response.ts
 * This is kept for backward compatibility but should be migrated.
 */

export async function scheduleFollowUps(contactId: string, response: BotResponse): Promise<void> {
  try {
    // Legacy implementation - using InstagramFollowUp table instead
    console.log('Legacy scheduleFollowUps called for contact:', contactId);

    // Create follow-ups using the InstagramFollowUp table
    const followUps = [];

    if (response.fu1_message && response.fu1_time) {
      followUps.push({
        contactId,
        message: response.fu1_message,
        scheduledTime: new Date(response.fu1_time),
        status: 'pending',
        sequenceNumber: 1
      });
    }

    if (response.fu2_message && response.fu2_time) {
      followUps.push({
        contactId,
        message: response.fu2_message,
        scheduledTime: new Date(response.fu2_time),
        status: 'pending',
        sequenceNumber: 2
      });
    }

    if (response.fu3_message && response.fu3_time) {
      followUps.push({
        contactId,
        message: response.fu3_message,
        scheduledTime: new Date(response.fu3_time),
        status: 'pending',
        sequenceNumber: 3
      });
    }

    if (response.fu4_message && response.fu4_time) {
      followUps.push({
        contactId,
        message: response.fu4_message,
        scheduledTime: new Date(response.fu4_time),
        status: 'pending',
        sequenceNumber: 4
      });
    }

    // Create follow-ups in the database
    for (const followUp of followUps) {
      await prisma.instagramFollowUp.create({
        data: {
          contactId: followUp.contactId,
          message: followUp.message,
          scheduledTime: followUp.scheduledTime,
          status: followUp.status as FollowUpStatus,
          sequenceNumber: followUp.sequenceNumber
        }
      });
    }

    console.log(`Scheduled ${followUps.length} follow-ups for contact ${contactId}`);
  } catch (error) {
    console.error('Error scheduling follow-ups:', error);
    throw new Error('Failed to schedule follow-ups');
  }
}

export async function processFollowUps(): Promise<void> {
  const now = new Date();

  // Find pending follow-ups that are due using the InstagramFollowUp table
  const dueFollowUps = await prisma.instagramFollowUp.findMany({
    where: {
      status: 'pending',
      scheduledTime: { lte: now }
    },
    include: {
      InstagramContact: {
        include: {
          Organization: {
            include: {
              InstagramSettings: true
            }
          }
        }
      }
    }
  });

  console.log(`Processing ${dueFollowUps.length} due follow-ups`);

  // Track organizations that need cache invalidation
  const organizationsToInvalidate = new Set<string>();

  for (const followUp of dueFollowUps) {
    const contact = followUp.InstagramContact;
    const instagramSettings = contact.Organization.InstagramSettings;

    if (!instagramSettings?.instagramToken) {
      console.log(`Missing Instagram token for organization ${contact.organizationId}`);
      continue;
    }

    // Check if follow-up is within 24-hour window
    const isWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, now);

    if (!isWithinWindow) {
      console.log(`Follow-up ${followUp.id} is outside 24-hour window. Last interaction: ${contact.lastInteractionAt}, Current time: ${now.toISOString()}`);

      // Mark as external (to be handled by external plugin)
      await prisma.instagramFollowUp.update({
        where: { id: followUp.id },
        data: {
          status: 'external'
        }
      });

      // Track organization for cache invalidation
      organizationsToInvalidate.add(contact.organizationId);
      continue;
    }

    const accessToken = instagramSettings.instagramToken;

    try {
      await sendFollowUpWithRetry(
        contact.id,
        contact.instagramId,
        followUp.message,
        accessToken,
        followUp.sequenceNumber,
        followUp.id
      );

      // Mark follow-up as sent (do not update lastInteractionAt for bot messages)
      await prisma.instagramFollowUp.update({
        where: { id: followUp.id },
        data: {
          status: 'sent',
          sentAt: new Date()
        }
      });

      // Track organization for cache invalidation
      organizationsToInvalidate.add(contact.organizationId);
      console.log(`Follow-up ${followUp.id} sent successfully within 24-hour window`);
    } catch (error) {
      console.error(`Error sending follow-up ${followUp.id}:`, error);

      // Check if we're still within 24-hour window after failure
      const isStillWithinWindow = isWithin24HourWindow(contact.lastInteractionAt, new Date());

      if (!isStillWithinWindow) {
        // Window expired during retry attempts, mark as external
        await prisma.instagramFollowUp.update({
          where: { id: followUp.id },
          data: { status: 'external' }
        });

        // Track organization for cache invalidation
        organizationsToInvalidate.add(contact.organizationId);
        console.log(`Follow-up ${followUp.id} moved to external handling due to 24h window expiration`);
      } else {
        // Mark follow-up as failed (actual failure within window)
        await prisma.instagramFollowUp.update({
          where: { id: followUp.id },
          data: { status: 'failed' }
        });

        // Track organization for cache invalidation
        organizationsToInvalidate.add(contact.organizationId);
      }
    }
  }

  // Invalidate cache for all affected organizations
  await invalidateCacheForOrganizations(organizationsToInvalidate);
}

async function sendFollowUpWithRetry(
  contactId: string,
  instagramId: string | null,
  message: string,
  accessToken: string,
  followUpNumber: number,
  followUpId: string,
  maxRetries: number = 3
): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await sendFollowUp(contactId, instagramId, message, accessToken, followUpNumber);
      console.log(`Follow-up ${followUpId} sent successfully on attempt ${attempt}`);
      return; // Success, exit retry loop
    } catch (error) {
      lastError = error as Error;
      console.error(`Follow-up ${followUpId} attempt ${attempt} failed:`, error);

      if (attempt < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
        console.log(`Retrying follow-up ${followUpId} in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // All retries failed
  throw lastError || new Error(`Failed to send follow-up ${followUpId} after ${maxRetries} attempts`);
}

async function sendFollowUp(contactId: string, instagramId: string | null, message: string, accessToken: string, followUpNumber: number): Promise<void> {
  if (!instagramId) {
    throw new Error('Instagram ID is required to send follow-up');
  }

  try {
    // Import here to avoid circular dependency
    const { sendMessage } = await import('@workspace/instagram/client');

    // Send the follow-up message
    await sendMessage({
      recipientId: instagramId,
      message,
      accessToken
    });

    // Save the message to the database
    await prisma.instagramMessage.create({
      data: {
        contactId,
        messageId: `follow-up-${followUpNumber}-${Date.now()}`,
        content: message,
        isFromUser: false
      }
    });

    console.log(`Follow-up ${followUpNumber} sent successfully to contact ${contactId}`);
  } catch (error) {
    console.error(`Error sending follow-up ${followUpNumber}:`, error);
    throw error; // Re-throw to be handled by caller
  }
}

/**
 * Invalidate cache for organizations via API call
 */
async function invalidateCacheForOrganizations(organizationIds: Set<string>): Promise<void> {
  if (organizationIds.size === 0) return;

  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.APP_URL || 'http://localhost:3000';
  const secret = process.env.CACHE_INVALIDATION_SECRET || 'cache-secret-123';

  for (const organizationId of organizationIds) {
    try {
      const response = await fetch(`${baseUrl}/api/cache/invalidate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organizationId,
          cacheKeys: [], // Instagram contacts cache removed
          secret
        })
      });

      if (!response.ok) {
        console.error(`Failed to invalidate cache for organization ${organizationId}:`, response.statusText);
      } else {
        console.log(`Cache invalidated for organization ${organizationId}`);
      }
    } catch (error) {
      console.error(`Error invalidating cache for organization ${organizationId}:`, error);
    }
  }
}
