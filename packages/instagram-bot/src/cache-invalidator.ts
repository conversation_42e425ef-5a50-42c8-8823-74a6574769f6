import axios from 'axios';

/**
 * Utility to invalidate cache in the dashboard app
 * NOTE: Instagram contacts cache has been removed - this function is now a no-op
 */
export async function invalidateInstagramContactsCache(organizationId: string): Promise<void> {
  // Cache invalidation removed - Instagram contacts no longer cached
  console.log(`Cache invalidation skipped for organization: ${organizationId} (Instagram contacts no longer cached)`);
}

/**
 * Invalidate multiple cache keys for an organization
 */
export async function invalidateOrganizationCache(
  organizationId: string,
  cacheKeys: string[]
): Promise<void> {
  try {
    const dashboardUrl = process.env.NEXT_PUBLIC_DASHBOARD_URL || 'http://localhost:3000';
    const cacheSecret = process.env.CACHE_INVALIDATION_SECRET || 'cache-secret-dev-12345';

    const baseUrl = dashboardUrl.replace(/\/$/, '');
    const invalidationUrl = `${baseUrl}/api/cache/invalidate`;

    console.log(`Invalidating cache for organization: ${organizationId}, keys:`, cacheKeys);

    const response = await axios.post(invalidationUrl, {
      organizationId,
      cacheKeys,
      secret: cacheSecret
    }, {
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log(`Successfully invalidated cache tags:`, response.data.invalidatedTags);
    } else {
      console.error('Cache invalidation failed:', response.data);
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.warn('Failed to invalidate cache (non-critical):', {
        status: error.response?.status,
        message: error.message,
        url: error.config?.url
      });
    } else {
      console.warn('Failed to invalidate cache (non-critical):', error);
    }
  }
}
